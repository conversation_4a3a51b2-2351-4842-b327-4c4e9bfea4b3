# Docker Environment Variable Configuration

This document explains how to properly configure the `BACKEND_API_URL` environment variable when running the application in Docker containers using server-side rendering.

## Solution Overview

This implementation uses **server-side environment variable injection** through Next.js server components. The environment variable is read on the server during rendering and injected into the client-side JavaScript.

## How It Works

1. **Server-Side**: The `ApiConfigProvider` component reads `BACKEND_API_URL` from server environment
2. **Injection**: The configuration is injected into the client via a `<Script>` tag with `beforeInteractive` strategy
3. **Client-Side**: The API client reads the configuration from `window.__API_CONFIG__`

## Usage

### 1. Build the Docker Image

```bash
docker build -t apartment-frontend .
```

### 2. Run with Environment Variable

```bash
# For local development with backend on host
docker run -p 3000:3000 \
  -e BACKEND_API_URL=http://localhost:3001 \
  apartment-frontend

# For production with external API
docker run -p 3000:3000 \
  -e BACKEND_API_URL=https://api.yourdomain.com \
  apartment-frontend

# For Docker Compose with backend service
docker run -p 3000:3000 \
  -e BACKEND_API_URL=http://backend:3001 \
  apartment-frontend
```

### 3. Using Docker Compose

Create a `docker-compose.yml` file:

```yaml
version: '3.8'

services:
  frontend:
    build: .
    ports:
      - "3000:3000"
    environment:
      - BACKEND_API_URL=http://backend:3001
    depends_on:
      - backend

  backend:
    image: your-backend-image:latest
    ports:
      - "3001:3001"
```

Then run:

```bash
docker-compose up
```

## Environment Variable Options

- **Development**: `http://localhost:3001`
- **Docker Compose**: `http://backend:3001` (using service name)
- **Production**: `https://api.yourdomain.com`
- **Custom**: Any valid URL where your backend API is running

## Verification

### Development Mode
In development, you'll see a debug panel in the bottom-right corner showing the current API configuration.

### Production Verification
1. Start the container with your desired API URL
2. Open browser DevTools → Console
3. Check `window.__API_CONFIG__` object
4. Verify API calls are going to the correct endpoint in Network tab

## Troubleshooting

### Container starts but API calls fail

1. Check if the backend is accessible from the frontend container
2. Verify the environment variable is set correctly:
   ```bash
   docker exec -it <container-id> printenv BACKEND_API_URL
   ```

### Configuration not loading

1. Check browser console for JavaScript errors
2. Verify `window.__API_CONFIG__` exists in browser DevTools
3. Check if the Script tag is present in the HTML source

### CORS issues

Make sure your backend API is configured to accept requests from the frontend domain.

## Technical Details

- **Server Component**: `src/components/ApiConfigProvider.tsx`
- **Client Integration**: `src/lib/api.ts`
- **Layout Integration**: `src/app/layout.tsx`
- **Debug Component**: `src/components/ApiConfigDebug.tsx` (development only)

The system uses Next.js `Script` component with `beforeInteractive` strategy to ensure the configuration is available before any client-side JavaScript executes.
