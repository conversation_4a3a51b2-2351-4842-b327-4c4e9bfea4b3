version: '3.8'

services:
  frontend:
    build: .
    ports:
      - "3000:3000"
    environment:
      # This environment variable will be available during server-side rendering
      - BACKEND_API_URL=http://backend:3001
      # You can also use different values for different environments:
      # - BACKEND_API_URL=https://api.yourdomain.com
      # - BACKEND_API_URL=http://localhost:3001
    depends_on:
      - backend
    networks:
      - app-network

  backend:
    # Your backend service configuration
    image: your-backend-image:latest
    ports:
      - "3001:3001"
    networks:
      - app-network

networks:
  app-network:
    driver: bridge
