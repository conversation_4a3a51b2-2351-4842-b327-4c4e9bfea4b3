{"name": "app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "node .next/standalone/server.js", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.50.0", "axios": "^1.10.0", "clsx": "^2.1.1", "date-fns": "^4.1.0", "file-saver": "^2.0.5", "jspdf": "^3.0.1", "jszip": "^3.10.1", "lucide-react": "^0.522.0", "next": "15.3.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.58.1", "tailwind-merge": "^3.3.1", "zod": "^3.25.67"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/file-saver": "^2.0.7", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4", "typescript": "^5"}}