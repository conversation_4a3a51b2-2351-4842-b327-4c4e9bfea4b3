#!/bin/bash

# Test script to verify environment variable configuration works

echo "🧪 Testing Environment Variable Configuration"
echo "============================================="

# Build the Docker image
echo "📦 Building Docker image..."
docker build -t apartment-frontend-test . || {
    echo "❌ Failed to build Docker image"
    exit 1
}

# Test 1: Default configuration
echo ""
echo "🔍 Test 1: Default configuration (should use localhost:3001)"
docker run --rm -d --name test-default -p 3001:3000 apartment-frontend-test
sleep 5

echo "Testing if container is running..."
docker ps | grep test-default || echo "Container not running"

docker stop test-default 2>/dev/null || true
echo "✅ Test 1 completed"

# Test 2: Custom API URL
echo ""
echo "🔍 Test 2: Custom API URL"
docker run --rm -d --name test-custom -p 3002:3000 \
    -e BACKEND_API_URL=https://api.example.com \
    apartment-frontend-test
sleep 5

echo "Testing if container is running..."
docker ps | grep test-custom || echo "Container not running"

docker stop test-custom 2>/dev/null || true
echo "✅ Test 2 completed"

# Test 3: Docker network URL
echo ""
echo "🔍 Test 3: Docker network URL"
docker run --rm -d --name test-network -p 3003:3000 \
    -e BACKEND_API_URL=http://backend:3001 \
    apartment-frontend-test
sleep 5

echo "Testing if container is running..."
docker ps | grep test-network || echo "Container not running"

docker stop test-network 2>/dev/null || true
echo "✅ Test 3 completed"

echo ""
echo "🎉 All tests completed!"
echo "You can now test the application by running:"
echo "docker run -p 3000:3000 -e BACKEND_API_URL=<your-api-url> apartment-frontend-test"
echo ""
echo "To verify the API URL is being used correctly:"
echo "1. Open http://localhost:3000 in your browser"
echo "2. Open browser DevTools → Console"
echo "3. Check for window.__API_CONFIG__ object"
