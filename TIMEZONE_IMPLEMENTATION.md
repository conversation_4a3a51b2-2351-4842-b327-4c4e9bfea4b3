# UTC+7 Timezone Implementation

This document explains how the frontend application handles timezone conversion to ensure all dates and times are consistently displayed in UTC+7 timezone.

## Overview

The application has been updated to display all dates and times in UTC+7 timezone (Asia/Bangkok) regardless of the user's browser timezone. This ensures consistency for all users and matches the expected timezone for the application.

## Implementation Details

### Core Utilities (`src/lib/utils.ts`)

All date formatting functions have been updated to use UTC+7 timezone:

#### Main Functions

- **`formatDate(date)`** - Formats date as "Jan 15, 2024" in UTC+7
- **`formatDateTime(date)`** - Formats date and time as "Jan 15, 2024, 10:30 AM" in UTC+7
- **`formatDateDisplay(date)`** - Formats with weekday as "Mon, Jan 15, 2024" in UTC+7
- **`formatDateForInput(date)`** - Formats for HTML date inputs as "2024-01-15" in UTC+7
- **`getCurrentDateUTC7()`** - Gets current date in UTC+7 formatted for inputs
- **`formatDateTimeFull(date)`** - Full format with timezone info
- **`toUTC7(date)`** - Converts any date to UTC+7 timezone

#### Usage Examples

```typescript
import { formatDate, formatDateTime, getCurrentDateUTC7 } from '@/lib/utils';

// Display billing date
const billingDate = formatDate('2024-01-15T03:30:00.000Z'); // "Jan 15, 2024"

// Display created timestamp
const createdAt = formatDateTime('2024-01-15T03:30:00.000Z'); // "Jan 15, 2024, 10:30 AM"

// Set default date for input
const defaultDate = getCurrentDateUTC7(); // "2024-01-15"
```

## Updated Components

### 1. PaymentModal (`src/components/PaymentModal.tsx`)

**Changes:**
- Payment date input now defaults to current date in UTC+7
- Billing date display uses `formatDate()` for UTC+7
- Payment history dates use `formatDate()` for UTC+7
- Date validation compares dates in UTC+7 timezone
- Max date for payment input uses UTC+7

**Before:**
```typescript
// Used browser timezone
new Date().toISOString().split("T")[0]
new Date(billingEntry.created_at).toLocaleDateString()
```

**After:**
```typescript
// Uses UTC+7 timezone
getCurrentDateUTC7()
formatDate(billingEntry.created_at)
```

### 2. Billing History (`src/app/billing-history/[id]/page.tsx`)

**Changes:**
- Billing entry dates display in UTC+7 using `formatDate()`

### 3. Room Details Modal (`src/components/rooms/RoomDetailsModal.tsx`)

**Changes:**
- Already using `formatDate()` from utils, automatically gets UTC+7 formatting

### 4. Billing Dashboard (`src/components/billing/BillingDashboard.tsx`)

**Changes:**
- Already using `formatDate()` from utils, automatically gets UTC+7 formatting

### 5. Billing History Component (`src/components/billing/BillingHistory.tsx`)

**Changes:**
- Already using `formatDate()` and `formatDateTime()` from utils, automatically gets UTC+7 formatting

## Testing

### Manual Testing

You can test the timezone conversion by:

1. **Browser Console Test:**
```javascript
// Import the test function (in development)
import { testTimezoneConversion } from '@/lib/timezone-test';
testTimezoneConversion();
```

2. **Component Testing:**
- Create billing entries and verify dates display consistently
- Add payments and check payment dates
- Compare displayed dates across different browser timezones

### Expected Behavior

- **Consistency:** All users see the same date/time regardless of their browser timezone
- **UTC+7 Display:** Dates should reflect UTC+7 timezone (7 hours ahead of UTC)
- **Input Fields:** Date inputs should default to current date in UTC+7
- **Validation:** Date validation should work correctly in UTC+7 context

## Migration Notes

### What Changed

1. **Date Formatting:** All date display functions now use UTC+7 timezone
2. **Input Defaults:** Date inputs default to current date in UTC+7
3. **Date Validation:** Date comparisons now happen in UTC+7 timezone
4. **Consistency:** No more dependency on browser's local timezone

### Backward Compatibility

- **Database:** No changes to database storage (still stores in UTC)
- **API:** No changes to API date formats
- **Existing Data:** All existing dates will display correctly in UTC+7

### Performance Impact

- **Minimal:** Uses native `Intl.DateTimeFormat` with timezone option
- **No External Dependencies:** Uses built-in browser APIs
- **Efficient:** Timezone conversion happens only during display

## Troubleshooting

### Common Issues

1. **Dates appear wrong:**
   - Verify the component is using utils functions (`formatDate`, `formatDateTime`)
   - Check if any components still use `toLocaleDateString()` directly

2. **Input dates don't match display:**
   - Ensure date inputs use `getCurrentDateUTC7()` for defaults
   - Verify max/min date attributes use UTC+7 functions

3. **Date validation fails:**
   - Check if validation logic uses `toUTC7()` for comparisons
   - Ensure both dates being compared are in UTC+7

### Debugging

```typescript
// Check if a date is being converted correctly
import { toUTC7, formatDate } from '@/lib/utils';

const testDate = '2024-01-15T03:30:00.000Z';
console.log('Original UTC:', new Date(testDate).toUTCString());
console.log('UTC+7 converted:', toUTC7(testDate));
console.log('Formatted display:', formatDate(testDate));
```

## Future Considerations

1. **User Preferences:** Could be extended to allow users to choose their preferred timezone
2. **Multiple Timezones:** Framework is in place to support other timezones if needed
3. **Daylight Saving:** Asia/Bangkok doesn't observe DST, so no additional handling needed
4. **Internationalization:** Date formats could be localized while maintaining UTC+7 timezone
