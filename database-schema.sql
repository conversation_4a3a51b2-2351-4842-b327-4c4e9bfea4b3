-- Apartment Management System Database Schema
-- Run these SQL commands in your Supabase SQL editor

-- Enable Row Level Security
ALTER DATABASE postgres SET "app.jwt_secret" TO 'your-jwt-secret';

-- Users table (extends Supabase auth.users)
CREATE TABLE IF NOT EXISTS users (
  id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
  email TEXT NOT NULL UNIQUE,
  role TEXT NOT NULL DEFAULT 'user' CHECK (role IN ('admin', 'user')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Buildings table
CREATE TABLE IF NOT EXISTS buildings (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  created_by UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Rooms table
CREATE TABLE IF NOT EXISTS rooms (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  building_id UUID REFERENCES buildings(id) ON DELETE CASCADE NOT NULL,
  room_number TEXT NOT NULL,
  monthly_rent DECIMAL(10,2) NOT NULL DEFAULT 0,
  initial_water_reading DECIMAL(10,2) NOT NULL DEFAULT 0,
  initial_electricity_reading DECIMAL(10,2) NOT NULL DEFAULT 0,
  current_water_reading DECIMAL(10,2) NOT NULL DEFAULT 0,
  current_electricity_reading DECIMAL(10,2) NOT NULL DEFAULT 0,
  status TEXT NOT NULL DEFAULT 'available' CHECK (status IN ('available', 'occupied', 'maintenance')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(building_id, room_number)
);

-- Additional services table
CREATE TABLE IF NOT EXISTS additional_services (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  room_id UUID REFERENCES rooms(id) ON DELETE CASCADE NOT NULL,
  name TEXT NOT NULL,
  price DECIMAL(10,2) NOT NULL DEFAULT 0,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Billing entries table
CREATE TABLE IF NOT EXISTS billing_entries (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  room_id UUID REFERENCES rooms(id) ON DELETE CASCADE NOT NULL,
  billing_date DATE NOT NULL,
  water_reading DECIMAL(10,2) NOT NULL,
  electricity_reading DECIMAL(10,2) NOT NULL,
  water_consumption DECIMAL(10,2) NOT NULL,
  electricity_consumption DECIMAL(10,2) NOT NULL,
  water_cost DECIMAL(10,2) NOT NULL,
  electricity_cost DECIMAL(10,2) NOT NULL,
  additional_services_cost DECIMAL(10,2) NOT NULL DEFAULT 0,
  total_amount DECIMAL(10,2) NOT NULL,
  created_by UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Utility pricing table
CREATE TABLE IF NOT EXISTS utility_pricing (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  water_price_per_unit DECIMAL(10,4) NOT NULL DEFAULT 0,
  electricity_price_per_unit DECIMAL(10,4) NOT NULL DEFAULT 0,
  billing_enabled BOOLEAN NOT NULL DEFAULT false,
  updated_by UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default utility pricing
INSERT INTO utility_pricing (water_price_per_unit, electricity_price_per_unit, billing_enabled, updated_by)
SELECT 0.50, 0.15, false, id FROM users WHERE role = 'admin' LIMIT 1
ON CONFLICT DO NOTHING;

-- Row Level Security Policies

-- Users table policies
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can view their own profile" ON users FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update their own profile" ON users FOR UPDATE USING (auth.uid() = id);

-- Buildings table policies
ALTER TABLE buildings ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Anyone can view buildings" ON buildings FOR SELECT USING (true);
CREATE POLICY "Only admins can create buildings" ON buildings FOR INSERT WITH CHECK (
  EXISTS (SELECT 1 FROM users WHERE id = auth.uid() AND role = 'admin')
);
CREATE POLICY "Only admins can update buildings" ON buildings FOR UPDATE USING (
  EXISTS (SELECT 1 FROM users WHERE id = auth.uid() AND role = 'admin')
);
CREATE POLICY "Only admins can delete buildings" ON buildings FOR DELETE USING (
  EXISTS (SELECT 1 FROM users WHERE id = auth.uid() AND role = 'admin')
);

-- Rooms table policies
ALTER TABLE rooms ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Anyone can view rooms" ON rooms FOR SELECT USING (true);
CREATE POLICY "Only admins can create rooms" ON rooms FOR INSERT WITH CHECK (
  EXISTS (SELECT 1 FROM users WHERE id = auth.uid() AND role = 'admin')
);
CREATE POLICY "Only admins can update rooms" ON rooms FOR UPDATE USING (
  EXISTS (SELECT 1 FROM users WHERE id = auth.uid() AND role = 'admin')
);
CREATE POLICY "Only admins can delete rooms" ON rooms FOR DELETE USING (
  EXISTS (SELECT 1 FROM users WHERE id = auth.uid() AND role = 'admin')
);

-- Additional services table policies
ALTER TABLE additional_services ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Anyone can view additional services" ON additional_services FOR SELECT USING (true);
CREATE POLICY "Only admins can manage additional services" ON additional_services FOR ALL USING (
  EXISTS (SELECT 1 FROM users WHERE id = auth.uid() AND role = 'admin')
);

-- Billing entries table policies
ALTER TABLE billing_entries ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Anyone can view billing entries" ON billing_entries FOR SELECT USING (true);
CREATE POLICY "Authenticated users can create billing entries" ON billing_entries FOR INSERT WITH CHECK (
  auth.uid() IS NOT NULL
);

-- Utility pricing table policies
ALTER TABLE utility_pricing ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Anyone can view utility pricing" ON utility_pricing FOR SELECT USING (true);
CREATE POLICY "Only admins can update utility pricing" ON utility_pricing FOR UPDATE USING (
  EXISTS (SELECT 1 FROM users WHERE id = auth.uid() AND role = 'admin')
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_buildings_created_by ON buildings(created_by);
CREATE INDEX IF NOT EXISTS idx_rooms_building_id ON rooms(building_id);
CREATE INDEX IF NOT EXISTS idx_rooms_status ON rooms(status);
CREATE INDEX IF NOT EXISTS idx_additional_services_room_id ON additional_services(room_id);
CREATE INDEX IF NOT EXISTS idx_billing_entries_room_id ON billing_entries(room_id);
CREATE INDEX IF NOT EXISTS idx_billing_entries_billing_date ON billing_entries(billing_date);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_buildings_updated_at BEFORE UPDATE ON buildings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_rooms_updated_at BEFORE UPDATE ON rooms FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_utility_pricing_updated_at BEFORE UPDATE ON utility_pricing FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
