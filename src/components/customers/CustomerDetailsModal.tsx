import React, { useState, useEffect } from "react";
import {
  Customer,
  CustomerRoomAssignment,
  IRoomCard,
  RoomNumberAndStatus,
} from "@/types";
import { customersService } from "@/lib/services/customers";
import { roomService } from "@/lib/services/rooms";
import Modal from "@/components/ui/Modal";
import Button from "@/components/ui/Button";
import Input from "@/components/ui/Input";
import { Label } from "@/components/ui/Label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/Card";
import {
  User,
  Phone,
  CreditCard,
  MapPin,
  Home,
  Plus,
  Trash2,
  Edit,
  Save,
  X,
} from "lucide-react";

interface CustomerDetailsModalProps {
  customer: Customer | null;
  isOpen: boolean;
  onClose: () => void;
  onUpdate: () => void;
}

const CustomerDetailsModal: React.FC<CustomerDetailsModalProps> = ({
  customer,
  isOpen,
  onClose,
  onUpdate,
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Edit form state
  const [editData, setEditData] = useState({
    nameEn: "",
    nameTh: "",
    idNumber: "",
    phoneNumber: "",
    address: "",
  });

  // Room assignment state
  const [availableRooms, setAvailableRooms] = useState<RoomNumberAndStatus[]>(
    []
  );
  const [selectedRoomId, setSelectedRoomId] = useState<string>("");
  const [assigningRoom, setAssigningRoom] = useState(false);
  const [customerRoomAssignments, setCustomerRoomAssignments] = useState<
    CustomerRoomAssignment[]
  >([]);

  useEffect(() => {
    if (customer && isOpen) {
      setEditData({
        nameEn: customer.nameEn,
        nameTh: customer.nameTh || "",
        idNumber: customer.idNumber,
        phoneNumber: customer.phoneNumber,
        address: customer.address || "",
      });
      fetchAvailableRooms();
    }
  }, [customer, isOpen]);

  const fetchAvailableRooms = async () => {
    if (!customer) return;

    try {
      const { data, error } =
        await roomService.getRoomNumbersAndStatusByBuilding(
          customer.buildingId
        );
      if (error) {
        console.error("Error fetching rooms:", error);
      } else if (data) {
        // Filter out rooms already assigned to this customer
        await GetCustomerRoomAssignments();
        SetAvailableRooms(data);
      }
    } catch (err) {
      console.error("Error fetching available rooms:", err);
    }
  };

  const GetCustomerRoomAssignments = async () => {
    if (!customer) return;

    try {
      setLoading(true);
      const { data, error } = await customersService.getRoomAssignments(
        customer.id
      );
      if (error) {
        console.error("Error fetching room assignments:", error);
      } else if (data) {
        customer.roomAssignments = data;
        setCustomerRoomAssignments(data);
      }
    } catch (err) {
      console.error("Error fetching customer room assignments:", err);
    } finally {
      setLoading(false);
    }
  };

  const SetAvailableRooms = (data: RoomNumberAndStatus[]) => {
    if (!customerRoomAssignments) return;

    const assignedRoomIds = customerRoomAssignments?.map((a) => a.roomId) || [];
    const available = data.filter((room) => !assignedRoomIds.includes(room.id));
    setAvailableRooms(available);
  };
  const handleSaveEdit = async () => {
    if (!customer) return;

    setLoading(true);
    setError(null);

    try {
      const { data, error } = await customersService.update(
        customer.id,
        editData
      );
      if (error) {
        setError(error);
      } else {
        setSuccess("Customer updated successfully");
        setIsEditing(false);
        onUpdate();
      }
    } catch (err) {
      setError("Failed to update customer");
    } finally {
      setLoading(false);
    }
  };

  const handleAssignRoom = async () => {
    if (!customer || !selectedRoomId) return;

    setAssigningRoom(true);
    setError(null);

    try {
      const { success, error } = await customersService.assignRoom(
        customer.id,
        selectedRoomId
      );
      if (error) {
        setError(error);
      } else {
        setSuccess("Room assigned successfully");
        setSelectedRoomId("");
        onUpdate();
        fetchAvailableRooms();
      }
    } catch (err) {
      setError("Failed to assign room");
    } finally {
      setAssigningRoom(false);
    }
  };

  const handleRemoveRoomAssignment = async (roomId: string) => {
    if (!customer) return;

    if (!confirm("Are you sure you want to remove this room assignment?")) {
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const { success, error } = await customersService.removeRoomAssignment(
        customer.id,
        roomId
      );
      if (error) {
        setError(error);
      } else {
        setSuccess("Room assignment removed successfully");
        onUpdate();
        fetchAvailableRooms();
      }
    } catch (err) {
      setError("Failed to remove room assignment");
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteCustomer = async () => {
    if (!customer) return;

    if (
      !confirm(
        "Are you sure you want to delete this customer? This action cannot be undone."
      )
    ) {
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const { success, error } = await customersService.delete(customer.id);
      if (error) {
        setError(error);
      } else {
        setSuccess("Customer deleted successfully");
        onUpdate();
        onClose();
      }
    } catch (err) {
      setError("Failed to delete customer");
    } finally {
      setLoading(false);
    }
  };

  if (!customer) return null;

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={`Customer Details - ${customer.nameEn}`}
      size="xl"
      maxHeight="lg"
    >
      <div className="space-y-6">
        {/* Success/Error Messages */}
        {success && (
          <div className="p-3 bg-green-50 border border-green-200 rounded-md">
            <p className="text-sm text-green-600">{success}</p>
          </div>
        )}

        {error && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-sm text-red-600">{error}</p>
          </div>
        )}

        {/* Customer Information */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center space-x-2">
                <User className="h-5 w-5" />
                <span>Customer Information</span>
              </CardTitle>
              <div className="flex space-x-2">
                {isEditing ? (
                  <>
                    <Button
                      size="sm"
                      onClick={handleSaveEdit}
                      disabled={loading}
                      className="flex items-center space-x-1"
                    >
                      <Save className="h-4 w-4" />
                      <span>Save</span>
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => setIsEditing(false)}
                      className="flex items-center space-x-1"
                    >
                      <X className="h-4 w-4" />
                      <span>Cancel</span>
                    </Button>
                  </>
                ) : (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setIsEditing(true)}
                    className="flex items-center space-x-1"
                  >
                    <Edit className="h-4 w-4" />
                    <span>Edit</span>
                  </Button>
                )}
              </div>
            </div>
          </CardHeader>

          <CardContent className="space-y-4">
            {isEditing ? (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="edit-nameEn">Name (English)</Label>
                  <Input
                    id="edit-nameEn"
                    value={editData.nameEn}
                    onChange={(e) =>
                      setEditData((prev) => ({
                        ...prev,
                        nameEn: e.target.value,
                      }))
                    }
                  />
                </div>
                <div>
                  <Label htmlFor="edit-nameTh">Name (Thai)</Label>
                  <Input
                    id="edit-nameTh"
                    value={editData.nameTh}
                    onChange={(e) =>
                      setEditData((prev) => ({
                        ...prev,
                        nameTh: e.target.value,
                      }))
                    }
                  />
                </div>
                <div>
                  <Label htmlFor="edit-idNumber">ID Number</Label>
                  <Input
                    id="edit-idNumber"
                    value={editData.idNumber}
                    onChange={(e) =>
                      setEditData((prev) => ({
                        ...prev,
                        idNumber: e.target.value,
                      }))
                    }
                  />
                </div>
                <div>
                  <Label htmlFor="edit-phoneNumber">Phone Number</Label>
                  <Input
                    id="edit-phoneNumber"
                    value={editData.phoneNumber}
                    onChange={(e) =>
                      setEditData((prev) => ({
                        ...prev,
                        phoneNumber: e.target.value,
                      }))
                    }
                  />
                </div>
                <div className="md:col-span-2">
                  <Label htmlFor="edit-address">Address</Label>
                  <textarea
                    id="edit-address"
                    value={editData.address}
                    onChange={(e) =>
                      setEditData((prev) => ({
                        ...prev,
                        address: e.target.value,
                      }))
                    }
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center space-x-2">
                  <User className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="font-medium">{customer.nameEn}</p>
                    {customer.nameTh && (
                      <p className="text-sm text-gray-600">{customer.nameTh}</p>
                    )}
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <CreditCard className="h-4 w-4 text-gray-500" />
                  <span>{customer.idNumber}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Phone className="h-4 w-4 text-gray-500" />
                  <span>{customer.phoneNumber}</span>
                </div>
                {customer.address && (
                  <div className="flex items-start space-x-2 md:col-span-2">
                    <MapPin className="h-4 w-4 text-gray-500 mt-1" />
                    <span>{customer.address}</span>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Room Assignments */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Home className="h-5 w-5" />
              <span>Room Assignments</span>
            </CardTitle>
          </CardHeader>

          <CardContent className="space-y-4">
            {/* Current Assignments */}
            {customerRoomAssignments && customerRoomAssignments.length > 0 ? (
              <div className="space-y-2">
                {customerRoomAssignments.map((assignment) => (
                  <div
                    key={assignment.id}
                    className="flex items-center justify-between p-3 bg-gray-50 rounded-md"
                  >
                    <div>
                      <p className="font-medium">
                        Room {assignment.room?.roomNumber}
                      </p>
                      <p className="text-sm text-gray-600">
                        Assigned on{" "}
                        {new Date(assignment.assignedAt).toLocaleDateString()}
                      </p>
                    </div>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() =>
                        handleRemoveRoomAssignment(assignment.roomId)
                      }
                      disabled={loading}
                      className="flex items-center space-x-1 text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4" />
                      <span>Remove</span>
                    </Button>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 text-center py-4">
                No rooms assigned
              </p>
            )}

            {/* Add Room Assignment */}
            {availableRooms.length > 0 && (
              <div className="border-t pt-4">
                <h4 className="font-medium mb-2">Assign New Room</h4>
                <div className="flex space-x-2">
                  <select
                    value={selectedRoomId}
                    onChange={(e) => setSelectedRoomId(e.target.value)}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">Select a room...</option>
                    {availableRooms.map((room) => (
                      <option key={room.id} value={room.id}>
                        Room {room.roomNumber} - {room.status}
                      </option>
                    ))}
                  </select>
                  <Button
                    onClick={handleAssignRoom}
                    disabled={!selectedRoomId || assigningRoom}
                    className="flex items-center space-x-1"
                  >
                    <Plus className="h-4 w-4" />
                    <span>Assign</span>
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Actions */}
        <div className="flex justify-between">
          <Button
            variant="outline"
            onClick={handleDeleteCustomer}
            disabled={loading}
            className="flex items-center space-x-1 text-red-600 hover:text-red-700"
          >
            <Trash2 className="h-4 w-4" />
            <span>Delete Customer</span>
          </Button>

          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default CustomerDetailsModal;
