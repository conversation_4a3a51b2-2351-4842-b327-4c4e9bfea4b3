import React, { useState, useEffect } from "react";
import { Customer, RoomCustomer } from "@/types";
import { customersService } from "@/lib/services/customers";
import Modal from "@/components/ui/Modal";
import CustomerCard from "./CustomerCard";
import { Users } from "lucide-react";

interface CustomerListModalProps {
  roomId: string;
  roomNumber: string;
  isOpen: boolean;
  onClose: () => void;
}

const CustomerListModal: React.FC<CustomerListModalProps> = ({
  roomId,
  roomNumber,
  isOpen,
  onClose,
}) => {
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Customer details modal state
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(
    null
  );

  useEffect(() => {
    if (isOpen && roomId) {
      fetchCustomers();
    }
  }, [isOpen, roomId]);

  const fetchCustomers = async () => {
    setLoading(true);
    setError(null);

    try {
      const { data, error } = await customersService.getCustomersByRoomId(
        roomId
      );
      if (error) {
        setError(error);
      } else if (data) {
        const customers =
          data?.flatMap((roomCustomer) =>
            roomCustomer.customer ? [roomCustomer.customer] : []
          ) || [];

        setCustomers(customers);
      }
    } catch (err) {
      setError("Failed to fetch customers");
    } finally {
      setLoading(false);
    }
  };

  const handleCustomerClick = () => {};

  return (
    <>
      <Modal
        isOpen={isOpen}
        onClose={onClose}
        title={`Customers - Room ${roomNumber}`}
        size="lg"
        maxHeight="lg"
      >
        <div className="space-y-4">
          {/* Header with count */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Users className="h-5 w-5 text-blue-600" />
              <div className="font-medium text-gray-900">
                {customers.length} customer{customers.length !== 1 ? "s" : ""}{" "}
                assigned
              </div>
            </div>
          </div>

          {/* Loading State */}
          {loading && (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="ml-3 text-gray-600">Loading customers...</span>
            </div>
          )}

          {/* Error State */}
          {error && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-sm text-red-600">{error}</p>
            </div>
          )}

          {/* Customer List */}
          {!loading && !error && (
            <>
              {customers.length === 0 ? (
                <div className="text-center py-8">
                  <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">
                    No customers assigned to this room
                  </p>
                  <p className="text-sm text-gray-400">
                    Customers can be assigned through the Customer Management
                    page
                  </p>
                </div>
              ) : (
                <div
                  className="flex flex-col gap-3 max-h-96 overflow-y-auto"
                  style={{ maxHeight: "400px" }}
                >
                  {customers.map((customer) => (
                    <CustomerCard
                      key={customer.id}
                      customer={customer}
                      onClick={handleCustomerClick}
                    />
                  ))}
                </div>
              )}
            </>
          )}
        </div>
      </Modal>
    </>
  );
};

export default CustomerListModal;
