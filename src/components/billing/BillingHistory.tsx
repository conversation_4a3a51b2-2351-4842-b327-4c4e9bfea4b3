import React, { useState, useEffect } from "react";
import { BillingEntry } from "@/types";
import { billingService } from "@/lib/services/billing";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import { formatCurrency, formatDate, formatDateTime } from "@/lib/utils";
import {
  Calendar,
  DollarSign,
  Droplets,
  Zap,
  User,
  Download,
  Filter,
  TrendingUp,
} from "lucide-react";

interface BillingHistoryProps {
  roomId: string;
  roomNumber: string;
}

const BillingHistory: React.FC<BillingHistoryProps> = ({
  roomId,
  roomNumber,
}) => {
  const [billingEntries, setBillingEntries] = useState<BillingEntry[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sortBy, setSortBy] = useState<"date" | "amount">("date");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");

  useEffect(() => {
    fetchBillingEntries();
  }, [roomId]);

  const fetchBillingEntries = async () => {
    setLoading(true);
    const { data, error } = await billingService.getBillingEntriesByRoom(
      roomId
    );

    if (error) {
      setError(error);
    } else if (data) {
      setBillingEntries(data);
    }

    setLoading(false);
  };

  const sortedEntries = [...billingEntries].sort((a, b) => {
    let comparison = 0;

    if (sortBy === "date") {
      comparison =
        new Date(a.billingDate).getTime() - new Date(b.billingDate).getTime();
    } else {
      comparison = a.totalAmount - b.totalAmount;
    }

    return sortOrder === "asc" ? comparison : -comparison;
  });

  const totalBilled = billingEntries.reduce(
    (sum, entry) => sum + entry.totalAmount,
    0
  );
  const averageBill =
    billingEntries.length > 0 ? totalBilled / billingEntries.length : 0;
  const lastBillingDate =
    billingEntries.length > 0 ? billingEntries[0].billingDate : null;

  const exportToCsv = () => {
    const headers = [
      "Date",
      "Water Reading",
      "Electricity Reading",
      "Water Consumption",
      "Electricity Consumption",
      "Water Cost",
      "Electricity Cost",
      "Additional Services",
      "Total Amount",
      "Created By",
      "Created At",
    ];

    const csvData = sortedEntries.map((entry) => [
      entry.billingDate,
      entry.waterReading,
      entry.electricityReading,
      entry.waterConsumption,
      entry.electricityConsumption,
      entry.waterCost,
      entry.electricityCost,
      entry.additionalServicesCost,
      entry.totalAmount,
      entry.createdBy,
      formatDateTime(entry.createdAt),
    ]);

    const csvContent = [headers, ...csvData]
      .map((row) => row.map((field) => `"${field}"`).join(","))
      .join("\n");

    const blob = new Blob([csvContent], { type: "text/csv" });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = `room-${roomNumber}-billing-history.csv`;
    link.click();
    window.URL.revokeObjectURL(url);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <div className="text-red-600 mb-4">
          Error loading billing history: {error}
        </div>
        <Button onClick={fetchBillingEntries}>Try Again</Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <DollarSign className="h-5 w-5 text-green-600" />
              <div>
                <div className="text-2xl font-bold text-green-600">
                  {formatCurrency(totalBilled)}
                </div>
                <div className="text-sm text-gray-500">Total Billed</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5 text-blue-600" />
              <div>
                <div className="text-2xl font-bold text-blue-600">
                  {formatCurrency(averageBill)}
                </div>
                <div className="text-sm text-gray-500">Average Bill</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Calendar className="h-5 w-5 text-purple-600" />
              <div>
                <div className="text-2xl font-bold text-purple-600">
                  {billingEntries.length}
                </div>
                <div className="text-sm text-gray-500">Total Entries</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Controls */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle className="flex items-center space-x-2">
                <Calendar className="h-5 w-5" />
                <span>Billing History</span>
              </CardTitle>
              <CardDescription>
                Complete billing history for Room {roomNumber}
              </CardDescription>
            </div>
            <div className="flex space-x-2">
              <div className="flex items-center space-x-2">
                <Filter className="h-4 w-4 text-gray-500" />
                <select
                  value={`${sortBy}-${sortOrder}`}
                  onChange={(e) => {
                    const [newSortBy, newSortOrder] = e.target.value.split(
                      "-"
                    ) as ["date" | "amount", "asc" | "desc"];
                    setSortBy(newSortBy);
                    setSortOrder(newSortOrder);
                  }}
                  className="text-sm border border-gray-300 rounded-md px-2 py-1"
                >
                  <option value="date-desc">Date (Newest First)</option>
                  <option value="date-asc">Date (Oldest First)</option>
                  <option value="amount-desc">Amount (Highest First)</option>
                  <option value="amount-asc">Amount (Lowest First)</option>
                </select>
              </div>
              <Button
                size="sm"
                variant="outline"
                onClick={exportToCsv}
                className="flex items-center space-x-1"
                disabled={billingEntries.length === 0}
              >
                <Download className="h-4 w-4" />
                <span>Export CSV</span>
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {billingEntries.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              No billing entries found for this room
            </div>
          ) : (
            <div className="space-y-4 max-h-96 overflow-y-auto">
              {sortedEntries.map((entry) => (
                <div
                  key={entry.id}
                  className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50"
                >
                  <div className="flex justify-between items-start mb-3">
                    <div>
                      <div className="font-semibold text-lg">
                        {formatDate(entry.billingDate)}
                      </div>
                      <div className="text-sm text-gray-500 flex items-center space-x-1">
                        <User className="h-3 w-3" />
                        <span>Created by {entry.createdBy}</span>
                        <span>•</span>
                        <span>{formatDateTime(entry.createdAt)}</span>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-2xl font-bold text-blue-600">
                        {formatCurrency(entry.totalAmount)}
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <div className="flex items-center space-x-1">
                          <Droplets className="h-4 w-4 text-blue-500" />
                          <span className="text-gray-600">Water:</span>
                        </div>
                        <span>
                          {entry.waterConsumption} units × $
                          {(
                            Number(entry.waterCost) /
                              Number(entry.waterConsumption) || 0
                          ).toFixed(4)}{" "}
                          = {formatCurrency(Number(entry.waterCost))}
                        </span>
                      </div>
                      <div className="text-xs text-gray-500 ml-5">
                        Reading: {entry.waterReading}
                      </div>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <div className="flex items-center space-x-1">
                          <Zap className="h-4 w-4 text-yellow-500" />
                          <span className="text-gray-600">Electricity:</span>
                        </div>
                        <span>
                          {entry.electricityConsumption} units × $
                          {(
                            Number(entry.electricityCost) /
                              Number(entry.electricityConsumption) || 0
                          ).toFixed(4)}{" "}
                          = {formatCurrency(Number(entry.electricityCost))}
                        </span>
                      </div>
                      <div className="text-xs text-gray-500 ml-5">
                        Reading: {entry.electricityReading}
                      </div>
                    </div>
                  </div>

                  {entry.additionalServicesCost > 0 && (
                    <div className="mt-2 pt-2 border-t border-gray-100">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">
                          Additional Services:
                        </span>
                        <span className="font-medium">
                          {formatCurrency(entry.additionalServicesCost)}
                        </span>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default BillingHistory;
