import React, { useState, useEffect } from "react";
import { buildingService } from "@/lib/services/buildings";
import { userManagementAPI } from "@/lib/api";
import { Building, User } from "@/types";
import Button from "@/components/ui/Button";
import Input from "@/components/ui/Input";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/Card";
import { Users, UserPlus, Trash2, Copy, CheckCircle } from "lucide-react";

interface BuildingUser extends User {
  accessGrantedAt: string;
}

interface BuildingUsersResponse {
  building: Building;
  users: BuildingUser[];
}

interface CreateUserResponse {
  user: User;
  credentials: {
    username: string;
    password: string;
  };
}

const UserManagement: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [creating, setCreating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [buildings, setBuildings] = useState<Building[]>([]);
  const [selectedBuildingId, setSelectedBuildingId] = useState<string | null>(
    null
  );
  const [buildingUsers, setBuildingUsers] =
    useState<BuildingUsersResponse | null>(null);
  const [showCredentials, setShowCredentials] = useState(false);
  const [newUserCredentials, setNewUserCredentials] = useState<
    CreateUserResponse["credentials"] | null
  >(null);
  const [customPassword, setCustomPassword] = useState("");
  const [useCustomPassword, setUseCustomPassword] = useState(false);
  const [customUsername, setCustomUsername] = useState("");
  const [useCustomUsername, setUseCustomUsername] = useState(false);
  const [usernameToAdd, setUsernameToAdd] = useState("");
  const [addingUser, setAddingUser] = useState(false);

  useEffect(() => {
    fetchBuildings();
  }, []);

  useEffect(() => {
    if (selectedBuildingId) {
      fetchBuildingUsers();
    }
  }, [selectedBuildingId]);

  const fetchBuildings = async () => {
    setLoading(true);
    const { data, error } = await buildingService.getBuildings();

    if (error) {
      setError(error);
    } else if (data) {
      setBuildings(data);
      if (data.length > 0) {
        setSelectedBuildingId(data[0].id);
      }
    }

    setLoading(false);
  };

  const fetchBuildingUsers = async () => {
    if (!selectedBuildingId) return;

    try {
      const response = await userManagementAPI.getBuildingUsers(
        selectedBuildingId
      );
      setBuildingUsers(response);
    } catch (err: unknown) {
      const errorMessage =
        err instanceof Error && "response" in err
          ? (err as { response?: { data?: { message?: string } } }).response
              ?.data?.message
          : "Failed to fetch building users";
      setError(errorMessage || "Failed to fetch building users");
    }
  };

  const createUser = async () => {
    if (!selectedBuildingId) return;

    setCreating(true);
    setError(null);
    setSuccess(null);

    try {
      console.log("Creating user with:", {
        buildingId: selectedBuildingId,
        useCustomPassword,
        customPassword,
        useCustomUsername,
        customUsername,
        finalPassword: useCustomPassword ? customPassword : undefined,
        finalUsername: useCustomUsername ? customUsername : undefined,
      });

      const response = await userManagementAPI.createUserForBuilding(
        selectedBuildingId,
        useCustomPassword ? customPassword : undefined,
        useCustomUsername ? customUsername : undefined
      );

      setNewUserCredentials(response.credentials);
      setShowCredentials(true);
      setSuccess("User created successfully!");
      setCustomPassword("");
      setUseCustomPassword(false);
      setCustomUsername("");
      setUseCustomUsername(false);

      // Refresh the users list
      await fetchBuildingUsers();
    } catch (err: unknown) {
      const errorMessage =
        err instanceof Error && "response" in err
          ? (err as { response?: { data?: { message?: string } } }).response
              ?.data?.message
          : "Failed to create user";
      setError(errorMessage || "Failed to create user");
    }

    setCreating(false);
  };

  const removeUser = async (userId: string) => {
    if (
      !selectedBuildingId ||
      !confirm("Are you sure you want to remove this user's access?")
    )
      return;

    try {
      await userManagementAPI.removeUserFromBuilding(
        selectedBuildingId,
        userId
      );
      setSuccess("User access removed successfully!");
      await fetchBuildingUsers();
    } catch (err: unknown) {
      const errorMessage =
        err instanceof Error && "response" in err
          ? (err as { response?: { data?: { message?: string } } }).response
              ?.data?.message
          : "Failed to remove user access";
      setError(errorMessage || "Failed to remove user access");
    }
  };

  const addUserByUsername = async () => {
    if (!selectedBuildingId || !usernameToAdd.trim()) return;

    setAddingUser(true);
    setError(null);
    setSuccess(null);

    try {
      const response = await userManagementAPI.addUserToBuilding(
        selectedBuildingId,
        usernameToAdd.trim()
      );

      setSuccess(
        `User ${response.user.username} successfully added to building!`
      );
      setUsernameToAdd("");

      // Refresh the users list
      await fetchBuildingUsers();
    } catch (err: unknown) {
      const errorMessage =
        err instanceof Error && "response" in err
          ? (err as { response?: { data?: { message?: string } } }).response
              ?.data?.message
          : "Failed to add user to building";
      setError(errorMessage || "Failed to add user to building");
    }

    setAddingUser(false);
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900 flex items-center space-x-2">
          <Users className="h-6 w-6" />
          <span>User Management</span>
        </h2>
        <p className="text-gray-600">
          Create and manage user accounts with building-specific access
        </p>
      </div>

      {/* Building Selector */}
      {buildings.length > 1 && (
        <Card>
          <CardHeader>
            <CardTitle>Select Building</CardTitle>
            <CardDescription>
              Choose which building to manage users for
            </CardDescription>
          </CardHeader>
          <CardContent>
            <select
              value={selectedBuildingId || ""}
              onChange={(e) => setSelectedBuildingId(e.target.value || null)}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">Select a building...</option>
              {buildings.map((building) => (
                <option key={building.id} value={building.id}>
                  {building.name}
                </option>
              ))}
            </select>
          </CardContent>
        </Card>
      )}

      {/* Status Messages */}
      {error && (
        <div className="p-4 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
          {error}
        </div>
      )}

      {success && (
        <div className="p-4 text-sm text-green-600 bg-green-50 border border-green-200 rounded-md">
          {success}
        </div>
      )}

      {/* Create User Section */}
      {selectedBuildingId && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <UserPlus className="h-5 w-5" />
              <span>Create New User</span>
            </CardTitle>
            <CardDescription>
              Create a new USER account with access to this building
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="useCustomUsername"
                checked={useCustomUsername}
                onChange={(e) => setUseCustomUsername(e.target.checked)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <label
                htmlFor="useCustomUsername"
                className="text-sm font-medium text-gray-700"
              >
                Set custom username
              </label>
            </div>

            {useCustomUsername && (
              <Input
                label="Custom Username"
                type="text"
                placeholder="Enter custom username"
                value={customUsername}
                onChange={(e) => setCustomUsername(e.target.value)}
              />
            )}

            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="useCustomPassword"
                checked={useCustomPassword}
                onChange={(e) => setUseCustomPassword(e.target.checked)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <label
                htmlFor="useCustomPassword"
                className="text-sm font-medium text-gray-700"
              >
                Set custom password
              </label>
            </div>

            {useCustomPassword && (
              <Input
                label="Custom Password"
                type="password"
                placeholder="Enter custom password"
                value={customPassword}
                onChange={(e) => setCustomPassword(e.target.value)}
              />
            )}

            <Button
              onClick={createUser}
              loading={creating}
              disabled={
                creating ||
                (useCustomPassword && !customPassword) ||
                (useCustomUsername && !customUsername)
              }
              className="flex items-center space-x-2"
            >
              <UserPlus className="h-4 w-4" />
              <span>Create User</span>
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Add User by Username Section */}
      {selectedBuildingId && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <UserPlus className="h-5 w-5" />
              <span>Add User by Username</span>
            </CardTitle>
            <CardDescription>
              Assign an existing user to this building by their username
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Input
              label="Username"
              type="text"
              placeholder="Enter username (e.g., john_doe)"
              value={usernameToAdd}
              onChange={(e) => setUsernameToAdd(e.target.value)}
            />

            <Button
              onClick={addUserByUsername}
              loading={addingUser}
              disabled={addingUser || !usernameToAdd.trim()}
              className="flex items-center space-x-2"
            >
              <UserPlus className="h-4 w-4" />
              <span>Add User to Building</span>
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Generated Credentials Modal */}
      {showCredentials && newUserCredentials && (
        <Card className="border-green-200 bg-green-50">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2 text-green-800">
              <CheckCircle className="h-5 w-5" />
              <span>User Created Successfully</span>
            </CardTitle>
            <CardDescription className="text-green-700">
              Please save these credentials and share them with the user
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">
                  Username
                </label>
                <div className="flex items-center space-x-2">
                  <Input
                    value={newUserCredentials.username}
                    readOnly
                    className="bg-white"
                  />
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => copyToClipboard(newUserCredentials.username)}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">
                  Password
                </label>
                <div className="flex items-center space-x-2">
                  <Input
                    value={newUserCredentials.password}
                    readOnly
                    className="bg-white"
                  />
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => copyToClipboard(newUserCredentials.password)}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>

            <div className="flex justify-end">
              <Button
                onClick={() => {
                  setShowCredentials(false);
                  setNewUserCredentials(null);
                }}
                variant="outline"
              >
                Close
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Building Users List */}
      {buildingUsers && (
        <Card>
          <CardHeader>
            <CardTitle>Building Users</CardTitle>
            <CardDescription>
              Users with access to {buildingUsers.building.name}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {buildingUsers.users.length === 0 ? (
              <p className="text-gray-500 text-center py-8">
                No users have access to this building yet.
              </p>
            ) : (
              <div className="space-y-4">
                {buildingUsers.users.map((user) => (
                  <div
                    key={user.id}
                    className="flex items-center justify-between p-4 border border-gray-200 rounded-lg"
                  >
                    <div>
                      <h4 className="font-medium text-gray-900">
                        {user.username}
                      </h4>
                      <p className="text-sm text-gray-500">{user.email}</p>
                      <p className="text-xs text-gray-400">
                        Access granted:{" "}
                        {new Date(user.accessGrantedAt).toLocaleDateString()}
                      </p>
                    </div>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => removeUser(user.id)}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default UserManagement;
