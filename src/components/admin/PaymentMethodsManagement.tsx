"use client";

import React, { useState, useEffect } from "react";
import Button from "@/components/ui/Button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/Card";
import Input from "@/components/ui/Input";
import { Label } from "@/components/ui/Label";
import { Textarea } from "@/components/ui/Textarea";
import { Switch } from "@/components/ui/Switch";
import { Plus, Edit, Trash2, Check, X } from "lucide-react";
import { paymentMethodsService } from "@/lib/services/payment-methods";
import { PaymentMethod } from "@/types";

interface PaymentMethodsManagementProps {
  onSuccess?: () => void;
}

const PaymentMethodsManagement: React.FC<PaymentMethodsManagementProps> = ({
  onSuccess,
}) => {
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [showAddForm, setShowAddForm] = useState(false);

  // Form state
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    isActive: true,
  });

  useEffect(() => {
    fetchPaymentMethods();
  }, []);

  const fetchPaymentMethods = async () => {
    setLoading(true);
    setError(null);

    const { data, error } = await paymentMethodsService.getAll();

    if (error) {
      setError(error);
    } else if (data) {
      setPaymentMethods(data);
    }

    setLoading(false);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setSuccess(null);

    if (editingId) {
      // Update existing payment method
      const { data, error } = await paymentMethodsService.update(
        editingId,
        formData
      );

      if (error) {
        setError(error);
      } else if (data) {
        setPaymentMethods((prev) =>
          prev.map((method) => (method.id === editingId ? data : method))
        );
        setSuccess("Payment method updated successfully");
        setEditingId(null);
        resetForm();
        onSuccess?.();
      }
    } else {
      // Create new payment method
      const { data, error } = await paymentMethodsService.create(formData);

      if (error) {
        setError(error);
      } else if (data) {
        setPaymentMethods((prev) => [data, ...prev]);
        setSuccess("Payment method created successfully");
        setShowAddForm(false);
        resetForm();
        onSuccess?.();
      }
    }
  };

  const handleEdit = (method: PaymentMethod) => {
    setFormData({
      name: method.name,
      description: method.description || "",
      isActive: method.isActive,
    });
    setEditingId(method.id);
    setShowAddForm(false);
  };

  const handleDelete = async (id: string) => {
    if (!confirm("Are you sure you want to delete this payment method?")) {
      return;
    }

    const { error } = await paymentMethodsService.delete(id);

    if (error) {
      setError(error);
    } else {
      setPaymentMethods((prev) => prev.filter((method) => method.id !== id));
      setSuccess("Payment method deleted successfully");
      onSuccess?.();
    }
  };

  const resetForm = () => {
    setFormData({
      name: "",
      description: "",
      isActive: true,
    });
    setEditingId(null);
    setShowAddForm(false);
  };

  const clearMessages = () => {
    setError(null);
    setSuccess(null);
  };

  const AddPaymentMethod = () => {
    setEditingId(null);
    resetForm();
    clearMessages();
    setShowAddForm(true);
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Payment Methods Management</CardTitle>
          <Button
            onClick={AddPaymentMethod}
            className="flex items-center space-x-2"
          >
            <Plus className="h-4 w-4" />
            <span>Add Payment Method</span>
          </Button>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Error/Success Messages */}
        {error && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-sm text-red-600">{error}</p>
          </div>
        )}

        {success && (
          <div className="p-3 bg-green-50 border border-green-200 rounded-md">
            <p className="text-sm text-green-600">{success}</p>
          </div>
        )}

        {/* Add/Edit Form */}
        {(showAddForm || editingId) && (
          <form
            onSubmit={handleSubmit}
            className="space-y-4 p-4 bg-gray-50 rounded-md"
          >
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium">
                {editingId ? "Edit Payment Method" : "Add New Payment Method"}
              </h3>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={resetForm}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="name">Name *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) =>
                    setFormData((prev) => ({ ...prev, name: e.target.value }))
                  }
                  placeholder="e.g., Credit Card"
                  required
                />
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="isActive"
                  checked={formData.isActive}
                  onCheckedChange={(checked) =>
                    setFormData((prev) => ({ ...prev, isActive: checked }))
                  }
                />
                <Label htmlFor="isActive">Active</Label>
              </div>
            </div>

            <div>
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    description: e.target.value,
                  }))
                }
                placeholder="Optional description of the payment method"
                rows={3}
              />
            </div>

            <div className="flex space-x-2">
              <Button type="submit" className="flex items-center space-x-2">
                <Check className="h-4 w-4" />
                <span>{editingId ? "Update" : "Create"}</span>
              </Button>
              <Button type="button" variant="outline" onClick={resetForm}>
                Cancel
              </Button>
            </div>
          </form>
        )}

        {/* Payment Methods List */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Existing Payment Methods</h3>

          {loading ? (
            <div className="text-center py-4">
              <p className="text-gray-500">Loading payment methods...</p>
            </div>
          ) : paymentMethods.length === 0 ? (
            <div className="text-center py-8 border-2 border-dashed border-gray-300 rounded-md">
              <p className="text-gray-500">No payment methods found</p>
              <p className="text-sm text-gray-400 mt-1">
                Click &quot;Add Payment Method&quot; to create one
              </p>
            </div>
          ) : (
            <div className="grid gap-4">
              {paymentMethods.map((method) => (
                <div
                  key={method.id}
                  className="flex items-center justify-between p-4 border rounded-md bg-white"
                >
                  <div className="flex-1">
                    <div className="flex items-center space-x-3">
                      <h4 className="font-medium">{method.name}</h4>
                      <span
                        className={`px-2 py-1 text-xs rounded-full ${
                          method.isActive
                            ? "bg-green-100 text-green-800"
                            : "bg-gray-100 text-gray-800"
                        }`}
                      >
                        {method.isActive ? "Active" : "Inactive"}
                      </span>
                    </div>
                    {method.description && (
                      <p className="text-sm text-gray-600 mt-1">
                        {method.description}
                      </p>
                    )}
                    <p className="text-xs text-gray-400 mt-1">
                      Created: {new Date(method.createdAt).toLocaleDateString()}
                      {method.user && ` by ${method.user.username}`}
                    </p>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleEdit(method)}
                      className="flex items-center space-x-1"
                    >
                      <Edit className="h-3 w-3" />
                      <span>Edit</span>
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDelete(method.id)}
                      className="flex items-center space-x-1 text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-3 w-3" />
                      <span>Delete</span>
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default PaymentMethodsManagement;
