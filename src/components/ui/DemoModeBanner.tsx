import React from "react";
// Demo mode is no longer needed with the new backend
const isDemoMode = false;
import { AlertTriangle, Info } from "lucide-react";

const DemoModeBanner: React.FC = () => {
  if (!isDemoMode) return null;

  return (
    <div className="bg-yellow-50 border-b border-yellow-200 px-4 py-3">
      <div className="max-w-7xl mx-auto flex items-center justify-center space-x-2">
        <AlertTriangle className="h-5 w-5 text-yellow-600" />
        <div className="text-sm text-yellow-800">
          <strong>Demo Mode:</strong> This application is running in demo mode.
          Set up Supabase credentials to enable full functionality.
        </div>
        <Info className="h-4 w-4 text-yellow-600" />
      </div>
    </div>
  );
};

export default DemoModeBanner;
