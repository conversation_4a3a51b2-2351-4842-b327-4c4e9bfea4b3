import Script from 'next/script';

interface ApiConfigProviderProps {
  children: React.ReactNode;
}

export default function ApiConfigProvider({ children }: ApiConfigProviderProps) {
  // Get the API URL from server-side environment variable
  const apiUrl = process.env.BACKEND_API_URL || "http://localhost:3001";
  
  // Create the configuration object
  const apiConfig = {
    BACKEND_API_URL: apiUrl
  };

  return (
    <>
      {/* Inject API configuration into the client */}
      <Script
        id="api-config"
        strategy="beforeInteractive"
        dangerouslySetInnerHTML={{
          __html: `
            window.__API_CONFIG__ = ${JSON.stringify(apiConfig)};
          `,
        }}
      />
      {children}
    </>
  );
}
