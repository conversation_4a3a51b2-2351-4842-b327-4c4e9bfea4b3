import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useAuth } from '@/lib/auth';
import { buildingService } from '@/lib/services/buildings';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import Modal from '@/components/ui/Modal';
import { Building } from '@/types';

const createBuildingSchema = z.object({
  name: z.string().min(1, 'Building name is required').max(100, 'Building name is too long'),
});

type CreateBuildingFormData = z.infer<typeof createBuildingSchema>;

interface CreateBuildingFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (building: Building) => void;
}

const CreateBuildingForm: React.FC<CreateBuildingFormProps> = ({
  isOpen,
  onClose,
  onSuccess,
}) => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<CreateBuildingFormData>({
    resolver: zodResolver(createBuildingSchema),
  });

  const onSubmit = async (data: CreateBuildingFormData) => {
    if (!user) return;

    setLoading(true);
    setError(null);

    const { data: building, error: createError } = await buildingService.createBuilding(
      data.name,
      user.id
    );

    if (createError) {
      setError(createError);
    } else if (building) {
      onSuccess(building);
      reset();
      onClose();
    }

    setLoading(false);
  };

  const handleClose = () => {
    reset();
    setError(null);
    onClose();
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title="Create New Building"
      size="md"
    >
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        {error && (
          <div className="p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
            {error}
          </div>
        )}

        <Input
          label="Building Name"
          placeholder="Enter building name"
          error={errors.name?.message}
          {...register('name')}
        />

        <div className="flex justify-end space-x-3 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={handleClose}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            loading={loading}
            disabled={loading}
          >
            Create Building
          </Button>
        </div>
      </form>
    </Modal>
  );
};

export default CreateBuildingForm;
