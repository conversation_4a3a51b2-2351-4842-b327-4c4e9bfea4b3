import React, { useState, useEffect } from "react";
import { Building } from "@/types";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import { formatDate } from "@/lib/utils";
import { buildingService } from "@/lib/services/buildings";
import { Building2, Users, DollarSign } from "lucide-react";

interface BuildingCardProps {
  building: Building;
  onSelect: (building: Building) => void;
  onEdit?: (building: Building) => void;
  onDelete?: (building: Building) => void;
  isAdmin?: boolean;
}

const BuildingCard: React.FC<BuildingCardProps> = ({
  building,
  onSelect,
  onEdit,
  onDelete,
  isAdmin = false,
}) => {
  const [stats, setStats] = useState({
    totalRooms: 0,
    occupiedRooms: 0,
    monthlyRevenue: 0,
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchStats = async () => {
      const { data, error } = await buildingService.getBuildingStats(
        building.id
      );
      if (data && !error) {
        setStats(data);
      }
      setLoading(false);
    };

    fetchStats();
  }, [building.id]);

  return (
    <Card className="hover:shadow-lg transition-shadow cursor-pointer">
      <CardHeader onClick={() => onSelect(building)}>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Building2 className="h-5 w-5 text-blue-600" />
            <CardTitle className="text-lg">{building.name}</CardTitle>
          </div>
          {isAdmin && (
            <div className="flex space-x-1">
              {onEdit && (
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={(e) => {
                    e.stopPropagation();
                    onEdit(building);
                  }}
                >
                  Edit
                </Button>
              )}
              {onDelete && (
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={(e) => {
                    e.stopPropagation();
                    onDelete(building);
                  }}
                >
                  Delete
                </Button>
              )}
            </div>
          )}
        </div>
        <CardDescription>
          Created on {formatDate(building.createdAt)}
        </CardDescription>
      </CardHeader>

      <CardContent onClick={() => onSelect(building)}>
        {loading ? (
          <div className="flex items-center justify-center py-4">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
          </div>
        ) : (
          <div className="grid grid-cols-3 gap-4 text-center">
            <div className="flex flex-col items-center">
              <Building2 className="h-4 w-4 text-gray-500 mb-1" />
              <div className="text-2xl font-bold text-gray-900">
                {stats.totalRooms}
              </div>
              <div className="text-xs text-gray-500">Total Rooms</div>
            </div>

            <div className="flex flex-col items-center">
              <Users className="h-4 w-4 text-green-500 mb-1" />
              <div className="text-2xl font-bold text-green-600">
                {stats.totalRooms - stats.occupiedRooms}
              </div>
              <div className="text-xs text-gray-500">Available</div>
            </div>

            <div className="flex flex-col items-center">
              <DollarSign className="h-4 w-4 text-blue-500 mb-1" />
              <div className="text-2xl font-bold text-blue-600">
                ${stats.monthlyRevenue.toLocaleString()}
              </div>
              <div className="text-xs text-gray-500">Monthly</div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default BuildingCard;
