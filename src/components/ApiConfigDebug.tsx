'use client';

import { useEffect, useState } from 'react';

export default function ApiConfigDebug() {
  const [config, setConfig] = useState<any>(null);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    if (typeof window !== 'undefined') {
      setConfig((window as any).__API_CONFIG__);
    }
  }, []);

  if (!mounted) {
    return null;
  }

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 bg-black bg-opacity-75 text-white p-3 rounded-lg text-xs max-w-sm">
      <div className="font-bold mb-2">API Config Debug</div>
      <div>
        <strong>Backend URL:</strong>{' '}
        {config?.BACKEND_API_URL || 'Not loaded'}
      </div>
      <div className="mt-1 text-gray-300">
        Config object: {config ? 'Loaded' : 'Not loaded'}
      </div>
    </div>
  );
}
