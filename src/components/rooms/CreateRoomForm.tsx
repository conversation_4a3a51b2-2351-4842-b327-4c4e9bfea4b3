import React, { useState } from "react";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { roomService } from "@/lib/services/rooms";
import Button from "@/components/ui/Button";
import Input from "@/components/ui/Input";
import Modal from "@/components/ui/Modal";
import { Plus, Trash2 } from "lucide-react";

const additionalServiceSchema = z.object({
  name: z.string().min(1, "Service name is required"),
  price: z.number().min(0, "Price must be positive"),
  description: z.string().optional(),
});

const createRoomSchema = z.object({
  roomNumber: z.string().min(1, "Room number is required"),
  monthlyRent: z.number().min(0, "Monthly rent must be positive"),
  initialWaterReading: z.number().min(0, "Water reading must be positive"),
  initialElectricityReading: z
    .number()
    .min(0, "Electricity reading must be positive"),
  additionalServices: z.array(additionalServiceSchema).optional(),
});

type CreateRoomFormData = z.infer<typeof createRoomSchema>;

interface CreateRoomFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  buildingId: string;
}

const CreateRoomForm: React.FC<CreateRoomFormProps> = ({
  isOpen,
  onClose,
  onSuccess,
  buildingId,
}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    control,
  } = useForm<CreateRoomFormData>({
    resolver: zodResolver(createRoomSchema),
    defaultValues: {
      additionalServices: [],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: "additionalServices",
  });

  const onSubmit = async (data: CreateRoomFormData) => {
    setLoading(true);
    setError(null);

    const { data: room, error: createError } = await roomService.createRoom({
      buildingId: buildingId,
      roomNumber: data.roomNumber,
      monthlyRent: data.monthlyRent,
      initialWaterReading: data.initialWaterReading,
      initialElectricityReading: data.initialElectricityReading,
      additionalServices: data.additionalServices,
    });

    if (createError) {
      setError(createError);
    } else if (room) {
      onSuccess();
      reset();
      onClose();
    }

    setLoading(false);
  };

  const handleClose = () => {
    reset();
    setError(null);
    onClose();
  };

  const addService = () => {
    append({ name: "", price: 0, description: "" });
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title="Create New Room"
      size="lg"
    >
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {error && (
          <div className="p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
            {error}
          </div>
        )}

        {/* Basic Room Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Input
            label="Room Number"
            placeholder="e.g., 101, A1, etc."
            error={errors.roomNumber?.message}
            {...register("roomNumber")}
          />

          <Input
            label="Monthly Rent ($)"
            type="number"
            step="0.01"
            placeholder="0.00"
            error={errors.monthlyRent?.message}
            {...register("monthlyRent", { valueAsNumber: true })}
          />
        </div>

        {/* Initial Meter Readings */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-3">
            Initial Meter Readings
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Water Meter Reading"
              type="number"
              step="0.01"
              placeholder="0.00"
              error={errors.initialWaterReading?.message}
              {...register("initialWaterReading", { valueAsNumber: true })}
            />

            <Input
              label="Electricity Meter Reading"
              type="number"
              step="0.01"
              placeholder="0.00"
              error={errors.initialElectricityReading?.message}
              {...register("initialElectricityReading", {
                valueAsNumber: true,
              })}
            />
          </div>
        </div>

        {/* Additional Services */}
        <div>
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-lg font-medium text-gray-900">
              Additional Services
            </h3>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={addService}
              className="flex items-center space-x-1"
            >
              <Plus size={16} />
              <span>Add Service</span>
            </Button>
          </div>

          {fields.length === 0 && (
            <p className="text-sm text-gray-500 italic">
              No additional services. Click "Add Service" to add services like
              parking, internet, etc.
            </p>
          )}

          <div className="space-y-3">
            {fields.map((field, index) => (
              <div
                key={field.id}
                className="p-4 border border-gray-200 rounded-md"
              >
                <div className="flex items-center justify-between mb-3">
                  <h4 className="text-sm font-medium text-gray-700">
                    Service {index + 1}
                  </h4>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => remove(index)}
                    className="text-red-600 hover:text-red-800"
                  >
                    <Trash2 size={16} />
                  </Button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <Input
                    label="Service Name"
                    placeholder="e.g., Parking, Internet"
                    error={errors.additionalServices?.[index]?.name?.message}
                    {...register(`additionalServices.${index}.name`)}
                  />

                  <Input
                    label="Price ($)"
                    type="number"
                    step="0.01"
                    placeholder="0.00"
                    error={errors.additionalServices?.[index]?.price?.message}
                    {...register(`additionalServices.${index}.price`, {
                      valueAsNumber: true,
                    })}
                  />
                </div>

                <div className="mt-3">
                  <Input
                    label="Description (Optional)"
                    placeholder="Brief description of the service"
                    error={
                      errors.additionalServices?.[index]?.description?.message
                    }
                    {...register(`additionalServices.${index}.description`)}
                  />
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="flex justify-end space-x-3 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={handleClose}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button type="submit" loading={loading} disabled={loading}>
            Create Room
          </Button>
        </div>
      </form>
    </Modal>
  );
};

export default CreateRoomForm;
