import React, { useState, useEffect, useMemo } from "react";
import {
  Room,
  BillingEntry,
  BuildingPricing,
  IRoomCard,
  BillingCycle,
} from "@/types";
import { useAuth } from "@/lib/auth";
import { billingService } from "@/lib/services/billing";
import Modal from "@/components/ui/Modal";
import Button from "@/components/ui/Button";
import Input from "@/components/ui/Input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/Card";
import { formatCurrency, formatDate, formatDateTimeThai } from "@/lib/utils";
import {
  Home,
  Droplets,
  Zap,
  Settings,
  Calendar,
  TrendingUp,
  Plus,
  User,
} from "lucide-react";
import { roomService } from "@/lib/services/rooms";
import CustomerListModal from "@/components/customers/CustomerListModal";

interface RoomDetailsModalProps {
  room: IRoomCard | null;
  isOpen: boolean;
  onClose: () => void;
  currentBillingCycle: BillingCycle;
}

const RoomDetailsModal: React.FC<RoomDetailsModalProps> = ({
  room,
  isOpen,
  onClose,
  currentBillingCycle,
}) => {
  const { user } = useAuth();
  const [roomDetail, setRoomDetail] = useState<Room | null>(null);
  const [billingEntries, setBillingEntries] = useState<BillingEntry[]>([]);
  const [buildingPricing, setBuildingPricing] =
    useState<BuildingPricing | null>(null);

  const [billingSummary, setBillingSummary] = useState({
    lastBillingDate: null as string | null,
    lastBillAmount: 0,
  });
  const [loading, setLoading] = useState(false);
  const [showBillingForm, setShowBillingForm] = useState(false);
  const [showCustomerListModal, setShowCustomerListModal] = useState(false);
  const [newWaterReading, setNewWaterReading] = useState("");
  const [newElectricityReading, setNewElectricityReading] = useState("");
  const [billingError, setBillingError] = useState<string | null>(null);
  const [submittingBilling, setSubmittingBilling] = useState(false);

  useEffect(() => {
    if (room && isOpen) {
      fetchRoomData();
      console.log("current billing cycle", currentBillingCycle);
    }
  }, [room, isOpen]);

  const wasPaidInCurrentCycle = useMemo(() => {
    const entry = billingEntries?.[0];

    return (
      entry !== null &&
      (entry?.status === "PAID" || entry?.status == "PARTIALLY_PAID") &&
      entry.billingCycleId === currentBillingCycle?.id
    );
  }, [currentBillingCycle, billingEntries]);

  const fetchRoomData = async () => {
    if (!room) return;

    setLoading(true);

    await fetchBillingEntries();

    await fetchBuildingPricing();

    await fetchRoomDetail();

    setLoading(false);
  };

  const fetchBuildingPricing = async () => {
    if (room?.buildingId) {
      const { data: pricing, error: pricingError } =
        await billingService.getBuildingPricing(room.buildingId);
      if (!pricingError && pricing) {
        setBuildingPricing(pricing);
      }
    }
  };

  const fetchBillingEntries = async () => {
    if (!room) return;

    const { data: entries, error: entriesError } =
      await billingService.getBillingEntriesByRoom(room.id);

    if (!entriesError && entries) {
      setBillingEntries(entries);
      setBillingSummary({
        lastBillingDate: entries[0]?.billingDate || null,
        lastBillAmount: entries[0]?.totalAmount || 0,
      });
    }
  };

  const fetchRoomDetail = async () => {
    if (!room) return;

    const { data: updatedRoom, error: roomError } =
      await roomService.getRoomById(room.id);

    if (!roomError && updatedRoom) {
      setRoomDetail(updatedRoom);
    }
  };

  const handleSubmitBilling = async () => {
    if (!roomDetail || !user) return;

    const waterReading = parseFloat(newWaterReading);
    const electricityReading = parseFloat(newElectricityReading);

    if (isNaN(waterReading) || isNaN(electricityReading)) {
      setBillingError("Please enter valid meter readings");
      return;
    }

    setSubmittingBilling(true);
    setBillingError(null);

    const { data: newEntry, error } = await billingService.createBillingEntry(
      roomDetail.id,
      roomDetail.currentWaterReading,
      roomDetail.currentElectricityReading,
      waterReading,
      electricityReading,
      currentBillingCycle.id
    );

    if (error) {
      setBillingError(error);
    } else if (newEntry) {
      // Refresh data
      await fetchRoomData();
      setShowBillingForm(false);
      setNewWaterReading("");
      setNewElectricityReading("");
    }

    setSubmittingBilling(false);
  };

  const getPaymentStatusColor = (paymentStatus: string) => {
    switch (paymentStatus) {
      case "PAID":
        return "bg-green-100 text-green-800 border border-green-200";
      case "UNPAID":
        return "bg-red-100 text-red-800 border border-red-200";
      case "PARTIALLY_PAID":
        return "bg-yellow-100 text-yellow-800 border border-yellow-200";
      case "UNKNOWN":
      case "":
      case undefined:
      case null:
        return "bg-gray-100 text-gray-800 border border-gray-200";
      default:
        return "bg-gray-100 text-gray-800 border border-gray-200";
    }
  };

  const getPaymentStatusIcon = (paymentStatus: string) => {
    switch (paymentStatus) {
      case "PAID":
        return "✅";
      case "UNPAID":
        return "❌";
      case "PARTIALLY_PAID":
        return "⚠️";
      case "UNKNOWN":
      case "":
      case undefined:
      case null:
        return "❓";
      default:
        return "❓";
    }
  };

  if (!roomDetail) return null;

  return (
    <>
      <Modal
        isOpen={isOpen}
        onClose={onClose}
        title={`Room ${roomDetail.roomNumber} Details`}
        size="xl"
        maxHeight="lg"
      >
        <div className="space-y-4 sm:space-y-6">
          <Card className="flex justify-between items-center p-2">
            <CardHeader>
              <CardTitle>Customers</CardTitle>
            </CardHeader>
            <Button onClick={() => setShowCustomerListModal(true)}>View</Button>
          </Card>
          {/* Room Overview */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Home className="h-5 w-5" />
                  <span>Room Information</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">Monthly Rent:</span>
                  <span className="font-medium">
                    {formatCurrency(roomDetail.monthlyRent)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Status:</span>
                  <span
                    className={`px-2 py-1 rounded-full text-xs font-medium ${
                      roomDetail.status === "AVAILABLE"
                        ? "bg-green-100 text-green-800"
                        : roomDetail.status === "OCCUPIED"
                        ? "bg-blue-100 text-blue-800"
                        : "bg-yellow-100 text-yellow-800"
                    }`}
                  >
                    {roomDetail.status.charAt(0).toUpperCase() +
                      roomDetail.status.slice(1)}
                  </span>
                </div>
                {roomDetail.status === "OCCUPIED" &&
                  room?.currentBillingDate !== null && (
                    <div className="flex items-center justify-between">
                      <div className="text-gray-600">
                        <div>Payment Status:</div>
                        <div>
                          ออกลบิลล่าสุด{" "}
                          {room?.currentBillingDate
                            ? formatDateTimeThai(room?.currentBillingDate)
                            : "Never"}
                        </div>
                      </div>
                      <div
                        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-semibold ${getPaymentStatusColor(
                          room?.paymentStatus || "UNKNOWN"
                        )}`}
                      >
                        {getPaymentStatusIcon(room?.paymentStatus || "UNKNOWN")}{" "}
                        {(room?.paymentStatus || "UNKNOWN").replace("_", " ")}
                      </div>
                    </div>
                  )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <TrendingUp className="h-5 w-5" />
                  <span>Billing Summary</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">Last Billing Date:</span>
                  <span className="font-medium">
                    {billingSummary.lastBillingDate
                      ? formatDate(billingSummary.lastBillingDate)
                      : "Never"}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Last Billing Amount:</span>
                  <span className="font-medium">
                    {billingSummary.lastBillAmount > 0
                      ? formatCurrency(billingSummary.lastBillAmount)
                      : "N/A"}
                  </span>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Current Meter Readings */}
          <Card>
            <CardHeader>
              <CardTitle>Current Meter Readings</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
                <div className="flex items-center space-x-3">
                  <Droplets className="h-8 w-8 text-blue-500" />
                  <div>
                    <div className="text-lg font-semibold">
                      {roomDetail.currentWaterReading}
                    </div>
                    <div className="text-sm text-gray-500">Water Meter</div>
                    <div className="text-xs text-gray-400">
                      Initial: {roomDetail.initialWaterReading}
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <Zap className="h-8 w-8 text-yellow-500" />
                  <div>
                    <div className="text-lg font-semibold">
                      {roomDetail.currentElectricityReading}
                    </div>
                    <div className="text-sm text-gray-500">
                      Electricity Meter
                    </div>
                    <div className="text-xs text-gray-400">
                      Initial: {roomDetail.initialElectricityReading}
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Additional Services */}
          {roomDetail.additionalServices &&
            roomDetail.additionalServices?.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Settings className="h-5 w-5" />
                    <span>Additional Services</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {roomDetail.additionalServices.map((service) => (
                      <div
                        key={service.id}
                        className="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0"
                      >
                        <div>
                          <div className="font-medium">{service.name}</div>
                          {service.description && (
                            <div className="text-sm text-gray-500">
                              {service.description}
                            </div>
                          )}
                        </div>
                        <div className="font-medium">
                          {formatCurrency(service.price)}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

          {/* Billing Entry Form */}
          {buildingPricing?.billingEnabled &&
            roomDetail.status == "OCCUPIED" &&
            !wasPaidInCurrentCycle && (
              <Card>
                <CardHeader>
                  <div className="flex justify-between items-center">
                    <CardTitle className="flex items-center space-x-2">
                      <Plus className="h-5 w-5" />
                      <span>Enter New Billing</span>
                    </CardTitle>
                    {!showBillingForm && (
                      <Button
                        onClick={() => setShowBillingForm(true)}
                        size="sm"
                      >
                        Add Billing Entry
                      </Button>
                    )}
                  </div>
                </CardHeader>
                {showBillingForm && (
                  <CardContent>
                    <div className="space-y-4">
                      {billingError && (
                        <div className="p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
                          {billingError}
                        </div>
                      )}

                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <Input
                          label="New Water Reading"
                          type="number"
                          step="0.01"
                          value={newWaterReading}
                          onChange={(e) => setNewWaterReading(e.target.value)}
                          placeholder={`Current: ${roomDetail.currentWaterReading}`}
                        />

                        <Input
                          label="New Electricity Reading"
                          type="number"
                          step="0.01"
                          value={newElectricityReading}
                          onChange={(e) =>
                            setNewElectricityReading(e.target.value)
                          }
                          placeholder={`Current: ${roomDetail.currentElectricityReading}`}
                        />
                      </div>

                      <div className="flex justify-end space-x-3">
                        <Button
                          variant="outline"
                          onClick={() => {
                            setShowBillingForm(false);
                            setBillingError(null);
                            setNewWaterReading("");
                            setNewElectricityReading("");
                          }}
                          disabled={submittingBilling}
                        >
                          Cancel
                        </Button>
                        <Button
                          onClick={handleSubmitBilling}
                          loading={submittingBilling}
                          disabled={submittingBilling}
                        >
                          Submit Billing
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                )}
              </Card>
            )}

          {/* Billing History */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Calendar className="h-5 w-5" />
                <span>Billing History</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                </div>
              ) : billingEntries.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  No billing entries yet
                </div>
              ) : (
                <div className="space-y-3 max-h-48 sm:max-h-64 overflow-y-auto">
                  {billingEntries.map((entry) => (
                    <div
                      key={entry.id}
                      className="p-3 sm:p-4 border border-gray-200 rounded-lg"
                    >
                      <div className="flex justify-between items-start mb-2">
                        <div>
                          <div className="text-sm sm:text-base font-medium">
                            {formatDate(entry.billingDate)}
                          </div>
                          <div className="text-xs sm:text-sm text-gray-500 flex items-center space-x-1">
                            <User className="h-3 w-3" />
                            <span>by {entry.createdBy}</span>
                          </div>
                        </div>
                        <div className="text-base sm:text-lg font-bold text-blue-600">
                          {formatCurrency(Number(entry.totalAmount))}
                        </div>
                      </div>

                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-4 text-xs sm:text-sm">
                        <div>
                          <span className="text-gray-600">Water: </span>
                          <span>
                            {entry.waterConsumption} units × $
                            {entry.waterCost / entry.waterConsumption || 0} ={" "}
                            {formatCurrency(entry.waterCost)}
                          </span>
                        </div>
                        <div>
                          <span className="text-gray-600">Electricity: </span>
                          <span>
                            {entry.electricityConsumption} units × $
                            {entry.electricityCost /
                              entry.electricityConsumption || 0}{" "}
                            = {formatCurrency(entry.electricityCost)}
                          </span>
                        </div>
                      </div>

                      {entry.additionalServicesCost > 0 && (
                        <div className="text-xs sm:text-sm mt-1">
                          <span className="text-gray-600">
                            Additional Services:{" "}
                          </span>
                          <span>
                            {formatCurrency(entry.additionalServicesCost)}
                          </span>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </Modal>

      {/* Customer List Modal */}
      {room && (
        <CustomerListModal
          roomId={room.id}
          roomNumber={room.roomNumber}
          isOpen={showCustomerListModal}
          onClose={() => setShowCustomerListModal(false)}
        />
      )}
    </>
  );
};

export default RoomDetailsModal;
