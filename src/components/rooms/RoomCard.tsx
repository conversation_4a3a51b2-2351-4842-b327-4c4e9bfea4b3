import React, { useMemo } from "react";
import { Billing<PERSON>ycle, IRoomCard } from "@/types";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import { formatCurrency, formatDateTimeThai } from "@/lib/utils";
import { Home, Droplets, Zap, Edit, Trash2 } from "lucide-react";

interface RoomCardProps {
  room: IRoomCard;
  onSelect: (room: IRoomCard) => void;
  onEdit?: (room: IRoomCard) => void;
  onDelete?: (room: IRoomCard) => void;
  isAdmin?: boolean;
  currentBillingCycle: BillingCycle;
}

const RoomCard: React.FC<RoomCardProps> = ({
  room,
  onSelect,
  onEdit,
  onDelete,
  isAdmin = false,
  currentBillingCycle,
}) => {
  const hasBillingEntry = useMemo(() => {
    return (
      currentBillingCycle?.status === "OPEN" &&
      room.currentBillingDate &&
      room.currentBillingDate > currentBillingCycle.startDate
    );
  }, [room.currentBillingDate, currentBillingCycle]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case "AVAILABLE":
        return "bg-green-100 text-green-800";
      case "OCCUPIED":
        return "bg-blue-100 text-blue-800";
      case "MAINTENANCE":
        return "bg-yellow-100 text-yellow-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "AVAILABLE":
        return "🟢";
      case "OCCUPIED":
        return "🔵";
      case "MAINTENANCE":
        return "🟡";
      default:
        return "⚪";
    }
  };

  const getPaymentStatusColor = (paymentStatus: string) => {
    switch (paymentStatus) {
      case "PAID":
        return "bg-green-100 text-green-800 border border-green-200";
      case "UNPAID":
        return "bg-red-100 text-red-800 border border-red-200";
      case "PARTIALLY_PAID":
        return "bg-yellow-100 text-yellow-800 border border-yellow-200";
      case "UNKNOWN":
      case "":
      case undefined:
      case null:
        return "bg-gray-100 text-gray-800 border border-gray-200";
      default:
        return "bg-gray-100 text-gray-800 border border-gray-200";
    }
  };

  const getPaymentStatusIcon = (paymentStatus: string) => {
    switch (paymentStatus) {
      case "PAID":
        return "✅";
      case "UNPAID":
        return "❌";
      case "PARTIALLY_PAID":
        return "⚠️";
      case "UNKNOWN":
      case "":
      case undefined:
      case null:
        return "❓";
      default:
        return "❓";
    }
  };

  const getBillingStatusColor = () => {
    return hasBillingEntry
      ? "bg-green-100 text-green-800 border border-green-200"
      : "bg-orange-100 text-orange-800 border border-orange-200";
  };

  const getBillingStatusIcon = () => {
    return hasBillingEntry ? "📋" : "📝";
  };

  const getBillingStatusText = () => {
    return hasBillingEntry ? "BILLED" : "NOT BILLED";
  };

  return (
    <Card className="hover:shadow-lg transition-shadow cursor-pointer">
      <CardHeader onClick={() => onSelect(room)}>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Home className="h-5 w-5 text-blue-600" />
            <CardTitle className="text-lg">Room {room.roomNumber}</CardTitle>
          </div>
          <div className="flex items-center space-x-2">
            <span
              className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(
                room.status
              )}`}
            >
              {getStatusIcon(room.status)}{" "}
              {room.status.charAt(0).toUpperCase() + room.status.slice(1)}
            </span>
            {isAdmin && (
              <div className="flex space-x-1">
                {onEdit && (
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={(e) => {
                      e.stopPropagation();
                      onEdit(room);
                    }}
                  >
                    <Edit size={16} />
                  </Button>
                )}
                {onDelete && (
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={(e) => {
                      e.stopPropagation();
                      onDelete(room);
                    }}
                  >
                    <Trash2 size={16} />
                  </Button>
                )}
              </div>
            )}
          </div>
        </div>
        <CardDescription>
          Monthly Rent: {formatCurrency(room.monthlyRent)}
        </CardDescription>
      </CardHeader>

      <CardContent onClick={() => onSelect(room)}>
        <div className="space-y-3">
          {/* Billing Entry Status */}
          {room.status == "OCCUPIED" && (
            <div className="flex items-center justify-between">
              <div className="text-sm font-medium text-gray-600">
                <div>รอบบิล</div>
                <div>{formatDateTimeThai(currentBillingCycle?.startDate)}</div>
              </div>
              <div
                className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-semibold ${getBillingStatusColor()}`}
              >
                {getBillingStatusIcon()} {getBillingStatusText()}
              </div>
            </div>
          )}

          {/* Payment Status */}
          {room.status == "OCCUPIED" && room.currentBillingDate !== null && (
            <div className="flex items-center justify-between">
              <div className="text-sm font-medium text-gray-600">
                <div>สถานะการชำระเงิน:</div>
                <div>
                  ออกบิลลล่าสุด {formatDateTimeThai(room.currentBillingDate)}
                </div>
              </div>
              <div
                className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-semibold ${getPaymentStatusColor(
                  room.paymentStatus || "UNKNOWN"
                )}`}
              >
                {getPaymentStatusIcon(room.paymentStatus || "UNKNOWN")}{" "}
                {(room.paymentStatus || "UNKNOWN").replace("_", " ")}
              </div>
            </div>
          )}

          {/* Meter Readings */}
          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-center space-x-2">
              <Droplets className="h-4 w-4 text-blue-500" />
              <div>
                <div className="text-sm font-medium">Water</div>
                <div className="text-xs text-gray-500">
                  Current: {room.currentWaterReading}
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Zap className="h-4 w-4 text-yellow-500" />
              <div>
                <div className="text-sm font-medium">Electricity</div>
                <div className="text-xs text-gray-500">
                  Current: {room.currentElectricityReading}
                </div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default RoomCard;
