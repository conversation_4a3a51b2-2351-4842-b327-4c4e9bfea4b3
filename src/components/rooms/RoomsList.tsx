import React, { useState, useEffect } from "react";
import { useAuth } from "@/lib/auth";
import { roomService } from "@/lib/services/rooms";
import { BillingCycle, BuildingPricing, IRoomCard } from "@/types";
import RoomCard from "./RoomCard";
import CreateRoomForm from "./CreateRoomForm";
import EditRoomForm from "./EditRoomForm";
import Button from "@/components/ui/Button";
import { Plus, Home, Filter } from "lucide-react";
import { useRouter } from "next/navigation";
import { billingService } from "@/lib/services/billing";

interface RoomsListProps {
  buildingId: string;
  buildingName: string;
  onRoomSelect: (room: IRoomCard) => void;
  currentBillingCycle: BillingCycle;
}

const RoomsList: React.FC<RoomsListProps> = ({
  buildingId,
  buildingName,
  onRoomSelect,
  currentBillingCycle,
}) => {
  const router = useRouter();
  const { isAdmin } = useAuth();
  const [rooms, setRooms] = useState<IRoomCard[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [showEditForm, setShowEditForm] = useState(false);
  const [editingRoom, setEditingRoom] = useState<IRoomCard | null>(null);
  const [statusFilter, setStatusFilter] = useState<string>("all");

  useEffect(() => {
    fetchRooms();
  }, [buildingId]);

  const fetchRooms = async () => {
    setLoading(true);
    const { data, error } = await roomService.getRoomsByBuilding(buildingId);

    if (error) {
      setError(error);
    } else if (data) {
      setRooms(data);
    }

    setLoading(false);
  };

  const handleCreateSuccess = () => {
    fetchRooms();
  };

  const handleEditRoom = (room: IRoomCard) => {
    setEditingRoom(room);
    setShowEditForm(true);
  };

  const handleEditSuccess = () => {
    fetchRooms();
    setShowEditForm(false);
    setEditingRoom(null);
  };

  const handleCloseEditForm = () => {
    setShowEditForm(false);
    setEditingRoom(null);
  };

  const handleDeleteRoom = async (room: IRoomCard) => {
    if (!confirm(`Are you sure you want to delete Room ${room.roomNumber}?`)) {
      return;
    }

    const { error } = await roomService.deleteRoom(room.id);

    if (error) {
      alert(`Failed to delete room: ${error}`);
    } else {
      setRooms((prev) => prev.filter((r) => r.id !== room.id));
    }
  };

  const filteredRooms = rooms.filter((room) => {
    if (statusFilter === "all") return true;
    return room.status === statusFilter.toUpperCase();
  });

  const getStatusCounts = () => {
    return {
      all: rooms.length,
      available: rooms.filter((r) => r.status === "AVAILABLE").length,
      occupied: rooms.filter((r) => r.status === "OCCUPIED").length,
      maintenance: rooms.filter((r) => r.status === "MAINTENANCE").length,
    };
  };

  const statusCounts = getStatusCounts();

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 mb-4">Error loading rooms: {error}</div>
        <Button onClick={fetchRooms}>Try Again</Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">
            Rooms in {buildingName}
          </h2>
          <p className="text-gray-600">
            {rooms.length} room{rooms.length !== 1 ? "s" : ""} total
          </p>
        </div>
        {isAdmin && (
          <div className="flex space-x-4">
            <Button
              variant="outline"
              onClick={() => router.push(`/billing-history/${buildingId}`)}
              className="flex items-center space-x-2 text-black"
            >
              Billing History
            </Button>
            <Button
              onClick={() => setShowCreateForm(true)}
              className="flex items-center space-x-2"
            >
              <Plus size={20} />
              <span>Add Room</span>
            </Button>
          </div>
        )}
      </div>

      {/* Status Filter */}
      <div className="flex items-center space-x-4">
        <div className="flex items-center space-x-2">
          <Filter className="h-4 w-4 text-gray-500" />
          <span className="text-sm font-medium text-gray-700">
            Filter by status:
          </span>
        </div>
        <div className="flex space-x-2">
          {[
            { key: "all", label: "All", count: statusCounts.all },
            {
              key: "available",
              label: "Available",
              count: statusCounts.available,
            },
            {
              key: "occupied",
              label: "Occupied",
              count: statusCounts.occupied,
            },
            {
              key: "maintenance",
              label: "Maintenance",
              count: statusCounts.maintenance,
            },
          ].map(({ key, label, count }) => (
            <button
              key={key}
              onClick={() => setStatusFilter(key)}
              className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                statusFilter === key
                  ? "bg-blue-100 text-blue-800"
                  : "bg-gray-100 text-gray-600 hover:bg-gray-200"
              }`}
            >
              {label} ({count})
            </button>
          ))}
        </div>
      </div>

      {/* Rooms Grid */}
      {filteredRooms.length === 0 ? (
        <div className="text-center py-12">
          <Home className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {statusFilter === "all"
              ? "No rooms yet"
              : `No ${
                  statusFilter === "occupied" ? "available" : statusFilter
                } rooms`}
          </h3>
          <p className="text-gray-600 mb-4">
            {statusFilter === "all"
              ? isAdmin
                ? "Get started by creating your first room."
                : "No rooms have been created yet."
              : `There are no rooms with ${statusFilter} status.`}
          </p>
          {isAdmin && statusFilter === "all" && (
            <Button onClick={() => setShowCreateForm(true)}>
              Create Your First Room
            </Button>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredRooms.map((room) => (
            <RoomCard
              key={room.id}
              room={room}
              onSelect={onRoomSelect}
              onEdit={isAdmin ? handleEditRoom : undefined}
              onDelete={isAdmin ? handleDeleteRoom : undefined}
              isAdmin={isAdmin}
              currentBillingCycle={currentBillingCycle}
            />
          ))}
        </div>
      )}

      {/* Create Room Form */}
      <CreateRoomForm
        isOpen={showCreateForm}
        onClose={() => setShowCreateForm(false)}
        onSuccess={handleCreateSuccess}
        buildingId={buildingId}
      />

      {/* Edit Room Form */}
      <EditRoomForm
        isOpen={showEditForm}
        onClose={handleCloseEditForm}
        onSuccess={handleEditSuccess}
        room={editingRoom}
      />
    </div>
  );
};

export default RoomsList;
