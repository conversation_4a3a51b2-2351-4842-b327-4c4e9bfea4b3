/**
 * Timezone Conversion Test/Demo
 *
 * This file demonstrates how the UTC+7 timezone conversion works
 * and can be used to test the functionality.
 */

import {
  formatDate,
  formatDateTime,
  formatDateDisplay,
  formatDateForInput,
  getCurrentDateUTC7,
  formatDateTimeFull,
  toUTC7,
} from "./utils";

// Test function to demonstrate timezone conversion
export function testTimezoneConversion() {
  console.log("=== UTC+7 Timezone Conversion Test ===");

  // Test with current date
  const now = new Date();
  console.log("Current browser time:", now.toString());
  console.log("Current UTC time:", now.toUTCString());
  console.log("Current UTC timestamp:", now.getTime());

  // Test our UTC+7 conversion functions
  console.log("\n--- Our UTC+7 Functions ---");
  const utc7Now = toUTC7(now);
  console.log("toUTC7 result:", utc7Now);
  console.log("toUTC7 timestamp:", utc7Now.getTime());
  console.log(
    "Time difference (hours):",
    (utc7Now.getTime() - now.getTime()) / (1000 * 60 * 60)
  );
  console.log("formatDate:", formatDate(now));
  console.log("formatDateTime:", formatDateTime(now));
  console.log("formatDateDisplay:", formatDateDisplay(now));
  console.log("formatDateForInput:", formatDateForInput(now));
  console.log("getCurrentDateUTC7:", getCurrentDateUTC7());
  console.log("formatDateTimeFull:", formatDateTimeFull(now));

  // Test with a specific date (useful for testing across timezones)
  const testDate = new Date("2024-01-15T10:30:00Z"); // UTC time
  console.log("\n--- Test with specific UTC date: 2024-01-15T10:30:00Z ---");
  console.log("Original UTC:", testDate.toUTCString());
  console.log("formatDate (UTC+7):", formatDate(testDate));
  console.log("formatDateTime (UTC+7):", formatDateTime(testDate));
  console.log("formatDateTimeFull (UTC+7):", formatDateTimeFull(testDate));

  // Test with date string from database
  const dbDateString = "2024-01-15T03:30:00.000Z";
  console.log("\n--- Test with DB date string:", dbDateString, "---");
  console.log("formatDate (UTC+7):", formatDate(dbDateString));
  console.log("formatDateTime (UTC+7):", formatDateTime(dbDateString));

  // Compare with browser's local formatting
  console.log("\n--- Comparison with browser local formatting ---");
  console.log("Browser toLocaleDateString():", now.toLocaleDateString());
  console.log("Our formatDate (UTC+7):", formatDate(now));
  console.log("Browser toLocaleString():", now.toLocaleString());
  console.log("Our formatDateTime (UTC+7):", formatDateTime(now));

  console.log("\n=== Test Complete ===");
}

// Example usage in components:
export const exampleUsage = {
  // For displaying dates in tables
  displayBillingDate: (dateString: string) => formatDate(dateString),

  // For displaying date and time
  displayCreatedAt: (dateString: string) => formatDateTime(dateString),

  // For date input fields
  setDefaultDate: () => getCurrentDateUTC7(),

  // For detailed date display
  displayFullDateTime: (dateString: string) => formatDateTimeFull(dateString),

  // For date comparisons
  compareDates: (date1: string, date2: string) => {
    const utc7Date1 = toUTC7(date1);
    const utc7Date2 = toUTC7(date2);
    return utc7Date1.getTime() - utc7Date2.getTime();
  },
};

// Validation helpers
export const dateValidation = {
  // Check if a date is in the future (UTC+7)
  isFutureDate: (dateString: string): boolean => {
    const inputDate = toUTC7(dateString);
    const today = toUTC7(new Date());
    return inputDate > today;
  },

  // Check if a date is today (UTC+7)
  isToday: (dateString: string): boolean => {
    const inputDate = toUTC7(dateString);
    const today = toUTC7(new Date());
    return inputDate.toDateString() === today.toDateString();
  },

  // Get start of day in UTC+7
  getStartOfDay: (dateString: string): Date => {
    const utc7Date = toUTC7(dateString);
    utc7Date.setHours(0, 0, 0, 0);
    return utc7Date;
  },

  // Get end of day in UTC+7
  getEndOfDay: (dateString: string): Date => {
    const utc7Date = toUTC7(dateString);
    utc7Date.setHours(23, 59, 59, 999);
    return utc7Date;
  },
};
