import { customersAPI } from "../api";
import {
  Customer,
  CreateCustomerData,
  UpdateCustomerData,
  CustomerRoomAssignment,
  RoomCustomer,
} from "@/types";

export const customersService = {
  // Get all customers for a building
  async getByBuilding(buildingId: string): Promise<{
    data: Customer[] | null;
    error: string | null;
  }> {
    try {
      const data = await customersAPI.getByBuilding(buildingId);
      return { data, error: null };
    } catch (error: any) {
      console.error("Error fetching customers:", error);
      return {
        data: null,
        error: error.response?.data?.message || "Failed to fetch customers",
      };
    }
  },

  // Get customer by ID
  async getById(id: string): Promise<{
    data: Customer | null;
    error: string | null;
  }> {
    try {
      const data = await customersAPI.getById(id);
      return { data, error: null };
    } catch (error: any) {
      console.error("Error fetching customer:", error);
      return {
        data: null,
        error: error.response?.data?.message || "Failed to fetch customer",
      };
    }
  },

  // Create customer
  async create(customerData: CreateCustomerData): Promise<{
    data: Customer | null;
    error: string | null;
  }> {
    try {
      const data = await customersAPI.create(customerData);
      return { data, error: null };
    } catch (error: any) {
      console.error("Error creating customer:", error);
      return {
        data: null,
        error: error.response?.data?.message || "Failed to create customer",
      };
    }
  },

  // Update customer
  async update(
    id: string,
    updates: UpdateCustomerData
  ): Promise<{
    data: Customer | null;
    error: string | null;
  }> {
    try {
      const data = await customersAPI.update(id, updates);
      return { data, error: null };
    } catch (error: any) {
      console.error("Error updating customer:", error);
      return {
        data: null,
        error: error.response?.data?.message || "Failed to update customer",
      };
    }
  },

  // Delete customer
  async delete(id: string): Promise<{
    success: boolean;
    error: string | null;
  }> {
    try {
      await customersAPI.delete(id);
      return { success: true, error: null };
    } catch (error: any) {
      console.error("Error deleting customer:", error);
      return {
        success: false,
        error: error.response?.data?.message || "Failed to delete customer",
      };
    }
  },

  //Get customers by room id
  async getCustomersByRoomId(roomId: string): Promise<{
    data: RoomCustomer[] | null;
    error: string | null;
  }> {
    try {
      const data = await customersAPI.getCustomersByRoomId(roomId);
      return { data, error: null };
    } catch (error: any) {
      console.error("Error fetching customers:", error);
      return {
        data: null,
        error: error.response?.data?.message || "Failed to fetch customers",
      };
    }
  },

  // Get room assignments for customer
  async getRoomAssignments(customerId: string): Promise<{
    data: CustomerRoomAssignment[] | null;
    error: string | null;
  }> {
    try {
      const data = await customersAPI.getRoomAssignments(customerId);
      return { data, error: null };
    } catch (error: any) {
      console.error("Error fetching room assignments:", error);
      return {
        data: null,
        error:
          error.response?.data?.message || "Failed to fetch room assignments",
      };
    }
  },

  // Assign room to customer
  async assignRoom(
    customerId: string,
    roomId: string
  ): Promise<{
    success: boolean;
    error: string | null;
  }> {
    try {
      await customersAPI.assignRoom(customerId, roomId);
      return { success: true, error: null };
    } catch (error: any) {
      console.error("Error assigning room:", error);
      return {
        success: false,
        error: error.response?.data?.message || "Failed to assign room",
      };
    }
  },

  // Remove room assignment
  async removeRoomAssignment(
    customerId: string,
    roomId: string
  ): Promise<{
    success: boolean;
    error: string | null;
  }> {
    try {
      await customersAPI.removeRoomAssignment(customerId, roomId);
      return { success: true, error: null };
    } catch (error: any) {
      console.error("Error removing room assignment:", error);
      return {
        success: false,
        error:
          error.response?.data?.message || "Failed to remove room assignment",
      };
    }
  },
};
