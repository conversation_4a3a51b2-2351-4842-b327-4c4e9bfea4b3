import { roomsAPI } from "@/lib/api";
import {
  Room,
  AdditionalService,
  IRoomCard,
  RoomNumberAndStatus,
} from "@/types";

// Type for updating room that matches backend DTO expectations
type UpdateRoomData = {
  roomNumber?: string;
  monthlyRent?: number;
  status?: "AVAILABLE" | "OCCUPIED" | "MAINTENANCE";
  add?: {
    name: string;
    price: number;
    description?: string;
  }[];
  update?: {
    id: string;
    name: string;
    price: number;
    description?: string;
  }[];
  remove?: {
    id: string;
  }[];
};

// Helper function to calculate service differences for updates
const calculateServiceDifferences = (
  originalServices: AdditionalService[],
  newServices: {
    name: string;
    price: number;
    description?: string;
    id?: string;
  }[]
) => {
  const add: { name: string; price: number; description?: string }[] = [];
  const update: {
    id: string;
    name: string;
    price: number;
    description?: string;
  }[] = [];
  const remove: { id: string }[] = [];

  // Find services to add (new services without IDs or with temporary IDs)
  newServices.forEach((service) => {
    if (!service.id || service.id.startsWith("temp_")) {
      add.push({
        name: service.name,
        price: service.price,
        description: service.description,
      });
    }
  });

  // Find services to update (existing services with changes)
  newServices.forEach((service) => {
    if (service.id && !service.id.startsWith("temp_")) {
      const original = originalServices.find((orig) => orig.id === service.id);
      if (
        original &&
        (original.name !== service.name ||
          original.price !== service.price ||
          original.description !== service.description)
      ) {
        update.push({
          id: service.id,
          name: service.name,
          price: service.price,
          description: service.description,
        });
      }
    }
  });

  // Find services to remove (original services not in new list)
  originalServices.forEach((original) => {
    const stillExists = newServices.find(
      (service) => service.id === original.id
    );
    if (!stillExists) {
      remove.push({ id: original.id });
    }
  });

  return { add, update, remove };
};

export const roomService = {
  //Get room numbers for a building
  async getRoomNumbersAndStatusByBuilding(
    buildingId: string
  ): Promise<{ data: RoomNumberAndStatus[] | null; error: string | null }> {
    try {
      const data = await roomsAPI.getRoomNumbersAndStatus(buildingId);
      return { data: data, error: null };
    } catch (error: any) {
      console.error("Error fetching room numbers:", error);
      return {
        data: null,
        error: error.response?.data?.message || "Failed to fetch room numbers",
      };
    }
  },

  // Get all rooms for a building
  async getRoomsByBuilding(
    buildingId: string
  ): Promise<{ data: IRoomCard[] | null; error: string | null }> {
    try {
      const data = await roomsAPI.getAll(buildingId);
      return { data, error: null };
    } catch (error: any) {
      console.error("Error fetching rooms:", error);
      return {
        data: null,
        error: error.response?.data?.message || "Failed to fetch rooms",
      };
    }
  },

  // Get room by ID
  async getRoomById(
    id: string
  ): Promise<{ data: Room | null; error: string | null }> {
    try {
      const data = await roomsAPI.getById(id);
      return { data, error: null };
    } catch (error: any) {
      console.error("Error fetching room:", error);
      return {
        data: null,
        error: error.response?.data?.message || "Failed to fetch room",
      };
    }
  },

  async findAdditionalServices(
    roomId: string
  ): Promise<{ data: AdditionalService[] | null; error: string | null }> {
    try {
      const data = await roomsAPI.findAdditionalServices(roomId);
      return { data, error: null };
    } catch (error: any) {
      console.error("Error fetching additional services:", error);
      return {
        data: null,
        error:
          error.response?.data?.message ||
          "Failed to fetch additional services",
      };
    }
  },
  // Create new room
  async createRoom(roomData: {
    buildingId: string;
    roomNumber: string;
    monthlyRent: number;
    initialWaterReading: number;
    initialElectricityReading: number;
    additionalServices?: {
      name: string;
      price: number;
      description?: string;
    }[];
  }): Promise<{ data: Room | null; error: string | null }> {
    try {
      const data = await roomsAPI.create(roomData.buildingId, {
        roomNumber: roomData.roomNumber.trim(),
        monthlyRent: roomData.monthlyRent,
        initialWaterReading: roomData.initialWaterReading,
        initialElectricityReading: roomData.initialElectricityReading,
        additionalServices: roomData.additionalServices || [],
        status: "AVAILABLE",
      });
      return { data, error: null };
    } catch (error: any) {
      console.error("Error creating room:", error);
      return {
        data: null,
        error: error.response?.data?.message || "Failed to create room",
      };
    }
  },

  // Update room
  async updateRoom(
    id: string,
    updates: UpdateRoomData
  ): Promise<{ data: Room | null; error: string | null }> {
    try {
      const data = await roomsAPI.updateDirect(id, updates);
      return { data, error: null };
    } catch (error: any) {
      console.error("Error updating room:", error);
      return {
        data: null,
        error: error.response?.data?.message || "Failed to update room",
      };
    }
  },

  // Update room with service differences calculation
  async updateRoomWithServices(
    id: string,
    roomData: {
      roomNumber?: string;
      monthlyRent?: number;
      status?: "AVAILABLE" | "OCCUPIED" | "MAINTENANCE";
    },
    originalServices: AdditionalService[],
    newServices: {
      name: string;
      price: number;
      description?: string;
      id?: string;
    }[]
  ): Promise<{ data: Room | null; error: string | null }> {
    try {
      const serviceDifferences = calculateServiceDifferences(
        originalServices,
        newServices
      );

      const updateData: UpdateRoomData = {
        ...roomData,
        ...serviceDifferences,
      };

      const data = await roomsAPI.updateDirect(id, updateData);
      return { data, error: null };
    } catch (error: any) {
      console.error("Error updating room with services:", error);
      return {
        data: null,
        error: error.response?.data?.message || "Failed to update room",
      };
    }
  },

  // Delete room
  async deleteRoom(id: string): Promise<{ error: string | null }> {
    try {
      await roomsAPI.deleteDirect(id);
      return { error: null };
    } catch (error: any) {
      console.error("Error deleting room:", error);
      return {
        error: error.response?.data?.message || "Failed to delete room",
      };
    }
  },
};
