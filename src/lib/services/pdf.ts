import js<PERSON><PERSON> from "jspdf";
import <PERSON><PERSON><PERSON><PERSON> from "jszip";
import { saveAs } from "file-saver";
import { BillingPdf } from "@/types";
import {
  formatDate,
  formatDateThai,
  numberToAmountNumberVM,
  numberToThaiBahtText,
} from "@/lib/utils";

export class PdfService {
  private static base64Font: string;

  private static readonly FONT_SIZE = {
    TITLE: 16,
    SUBTITLE: 14,
    NORMAL: 12,
    SMALL: 10,
  };

  private static readonly COLORS = {
    PRIMARY: [31, 41, 55] as const, // RGB values for #1f2937
    SECONDARY: [107, 114, 128] as const, // RGB values for #6b7280
    ACCENT: [59, 130, 246] as const, // RGB values for #3b82f6
  };

  private static readonly MARGINS = {
    TOP: 20,
    LEFT: 20,
    RIGHT: 20,
    BOTTOM: 20,
  };

  private static async fetchBase64Font(): Promise<string> {
    if (this.base64Font) {
      return this.base64Font;
    }

    const response = await fetch("/fonts/Sarabun-Regular.ttf");
    const buffer = await response.arrayBuffer();

    // Convert to base64
    const binary = new Uint8Array(buffer).reduce(
      (data, byte) => data + String.fromCharCode(byte),
      ""
    );
    this.base64Font = btoa(binary);
    return this.base64Font;
  }

  /**
   * Generate a single PDF for billing data
   */
  static async generateBillingPdf(billingData: BillingPdf): Promise<jsPDF> {
    try {
      console.log("Creating new jsPDF instance");
      const doc = new jsPDF();

      // Try to add Thai font if available
      const base64Font = await this.fetchBase64Font();

      doc.addFileToVFS("Sarabun-Regular-normal.ttf", base64Font);
      doc.addFont("Sarabun-Regular-normal.ttf", "Sarabun-Regular", "normal");
      doc.setFont("Sarabun-Regular");

      const pageWidth = doc.internal.pageSize.getWidth();
      const pageHeight = doc.internal.pageSize.getHeight();

      let yPosition = this.MARGINS.TOP;

      // Header
      doc.setFontSize(this.FONT_SIZE.TITLE);
      doc.setTextColor(...this.COLORS.PRIMARY);
      const titleText = "BILLING INVOICE";
      const titleWidth = doc.getTextWidth(titleText);
      doc.text(titleText, (pageWidth - titleWidth) / 2, yPosition);
      yPosition += 15;

      // Building and Customer Info
      doc.setFontSize(this.FONT_SIZE.NORMAL);
      doc.text(`${billingData.buildingName}`, this.MARGINS.LEFT, yPosition);
      yPosition += 8;
      doc.text(
        `ชื่อ: ${billingData.customerName}`,
        this.MARGINS.LEFT,
        yPosition
      );
      yPosition += 8;
      doc.text(`ห้อง: ${billingData.roomNumber}`, this.MARGINS.LEFT, yPosition);
      yPosition += 8;
      doc.text(
        `งวดวันที่: ${formatDateThai(billingData.billingEnabledDate)}`,
        this.MARGINS.LEFT,
        yPosition
      );
      yPosition += 15;

      // Table Header
      const colWidths = [10, 100, 30, 40]; // Description, Unit, Amount
      const colPositions = [
        this.MARGINS.LEFT,
        this.MARGINS.LEFT + colWidths[0],
        this.MARGINS.LEFT + colWidths[0] + colWidths[1],
        this.MARGINS.LEFT + colWidths[0] + colWidths[1] + colWidths[2],
      ];

      // Draw table header
      doc.setFillColor(240, 240, 240);
      doc.rect(
        this.MARGINS.LEFT,
        yPosition,
        pageWidth - this.MARGINS.LEFT - this.MARGINS.RIGHT,
        10,
        "F"
      );

      doc.setFontSize(this.FONT_SIZE.NORMAL);
      doc.setTextColor(...this.COLORS.PRIMARY);
      doc.text("#", colPositions[0] + 2, yPosition + 7);
      doc.text("Description", colPositions[1] + 2, yPosition + 7);
      doc.text("Unit", colPositions[2] + 2, yPosition + 7);
      doc.text("Amount (THB)", colPositions[3] + 2, yPosition + 7);
      yPosition += 10;

      // Table Rows
      let totalAmount = 0;
      billingData.tableRows.forEach((row, index) => {
        // Alternate row colors
        if (index % 2 === 0) {
          doc.setFillColor(250, 250, 250);
          doc.rect(
            this.MARGINS.LEFT,
            yPosition,
            pageWidth - this.MARGINS.LEFT - this.MARGINS.RIGHT,
            8,
            "F"
          );
        }

        doc.setFontSize(this.FONT_SIZE.SMALL);
        doc.setTextColor(...this.COLORS.SECONDARY);

        //#
        doc.text(`${index + 1}`, colPositions[0] + 2, yPosition + 6);

        // Description (with text wrapping if needed)
        const descriptionLines = doc.splitTextToSize(
          row.description,
          colWidths[1] - 4
        );
        doc.text(descriptionLines, colPositions[1] + 2, yPosition + 6);

        // Unit
        doc.text(row.unit, colPositions[2] + 2, yPosition + 6);

        // Amount
        doc.text(
          numberToAmountNumberVM(row.amount),
          colPositions[3] + 2,
          yPosition + 6
        );

        totalAmount += row.amount;
        yPosition += Math.max(8, descriptionLines.length * 4);
      });

      // Total
      yPosition += 5;
      doc.setFillColor(230, 230, 230);
      doc.rect(
        this.MARGINS.LEFT,
        yPosition,
        pageWidth - this.MARGINS.LEFT - this.MARGINS.RIGHT,
        10,
        "F"
      );

      doc.setFontSize(this.FONT_SIZE.NORMAL);
      doc.setTextColor(...this.COLORS.PRIMARY);
      doc.text(
        `TOTAL ${numberToThaiBahtText(totalAmount)}`,
        colPositions[0] + 2,
        yPosition + 7
      );
      doc.text(
        `${numberToAmountNumberVM(totalAmount)} THB`,
        colPositions[3] - 3,
        yPosition + 7
      );

      // Footer
      yPosition = pageHeight - this.MARGINS.BOTTOM - 10;
      doc.setFontSize(this.FONT_SIZE.SMALL);
      doc.setTextColor(...this.COLORS.SECONDARY);
      doc.text(
        `Generated on ${formatDate(new Date().toISOString())}`,
        this.MARGINS.LEFT,
        yPosition
      );

      return doc;
    } catch (error) {
      console.error("Error in generateBillingPdf:", error);
      throw error;
    }
  }

  /**
   * Download a single PDF
   */
  static async downloadSinglePdf(billingData: BillingPdf): Promise<void> {
    try {
      console.log("Starting PDF generation with data:");
      const doc = await this.generateBillingPdf(billingData);
      console.log("PDF generated successfully");

      const fileName = `billing-${billingData.roomNumber}-${formatDate(
        billingData.billingEnabledDate
      ).replace(/\//g, "-")}.pdf`;
      console.log("Saving PDF with filename:", fileName);

      doc.save(fileName);
      console.log("PDF download initiated");
    } catch (error) {
      console.error("Error in downloadSinglePdf:", error);
      throw error;
    }
  }

  /**
   * Generate and download multiple PDFs as a ZIP file
   */
  static async downloadBulkPdfs(
    billingDataArray: BillingPdf[],
    zipFileName: string,
    onProgress?: (current: number, total: number) => void
  ): Promise<void> {
    const zip = new JSZip();

    for (let i = 0; i < billingDataArray.length; i++) {
      const billingData = billingDataArray[i];
      const doc = await this.generateBillingPdf(billingData);
      const fileName = `billing-${billingData.roomNumber}-${formatDate(
        billingData.billingEnabledDate
      ).replace(/\//g, "-")}.pdf`;

      // Convert PDF to blob and add to ZIP
      const pdfBlob = doc.output("blob");
      zip.file(fileName, pdfBlob);

      // Report progress
      if (onProgress) {
        onProgress(i + 1, billingDataArray.length);
      }
    }

    // Generate ZIP file and download
    const zipBlob = await zip.generateAsync({ type: "blob" });
    saveAs(zipBlob, zipFileName);
  }

  /**
   * Generate filename for ZIP file
   */
  static generateZipFileName(buildingName: string, dateStr?: string): string {
    const sanitizedBuildingName = buildingName.replace(/[^a-zA-Z0-9]/g, "-");

    return `billing-history-${sanitizedBuildingName}${dateStr}.zip`;
  }

  /**
   * Test function to verify PDF generation works
   */
  static testPdfGeneration(): void {
    try {
      const testData: BillingPdf = {
        buildingName: "Test Building",
        customerName: "Test Customer",
        roomNumber: "A101",
        billingEnabledDate: "2024-01-01",
        tableRows: [
          {
            description: "Monthly Rent",
            unit: "",
            amount: 1000,
          },
          {
            description: "Water Usage",
            unit: "10",
            amount: 50,
          },
        ],
      };

      console.log("Testing PDF generation with test data");
      this.downloadSinglePdf(testData);
    } catch (error) {
      console.error("Test PDF generation failed:", error);
    }
  }
}
