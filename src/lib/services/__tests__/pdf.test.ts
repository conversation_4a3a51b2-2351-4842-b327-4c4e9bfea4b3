import { PdfService } from '../pdf';
import { BillingPdf } from '@/types';

// Mock the external dependencies
jest.mock('jspdf', () => {
  return jest.fn().mockImplementation(() => ({
    internal: {
      pageSize: {
        getWidth: () => 210,
        getHeight: () => 297,
      },
    },
    setFontSize: jest.fn(),
    setTextColor: jest.fn(),
    setFillColor: jest.fn(),
    text: jest.fn(),
    rect: jest.fn(),
    splitTextToSize: jest.fn().mockReturnValue(['Test line']),
    output: jest.fn().mockReturnValue(new Blob()),
    save: jest.fn(),
  }));
});

jest.mock('jszip', () => {
  return jest.fn().mockImplementation(() => ({
    file: jest.fn(),
    generateAsync: jest.fn().mockResolvedValue(new Blob()),
  }));
});

jest.mock('file-saver', () => ({
  saveAs: jest.fn(),
}));

describe('PdfService', () => {
  const mockBillingData: BillingPdf = {
    buildingName: 'Test Building',
    customerName: 'John Doe',
    roomNumber: 'A101',
    billingEnabledDate: '2024-01-01',
    tableRows: [
      {
        description: 'Monthly Rent',
        unit: '',
        amount: 1000,
      },
      {
        description: 'Water Usage',
        unit: '10',
        amount: 50,
      },
      {
        description: 'Electricity Usage',
        unit: '100',
        amount: 200,
      },
    ],
  };

  describe('generateBillingPdf', () => {
    it('should generate a PDF document', () => {
      const pdf = PdfService.generateBillingPdf(mockBillingData);
      expect(pdf).toBeDefined();
    });
  });

  describe('generateZipFileName', () => {
    it('should generate a proper ZIP filename', () => {
      const fileName = PdfService.generateZipFileName('Test Building', '1', '2024');
      expect(fileName).toBe('billing-history-Test-Building-1-2024.zip');
    });

    it('should sanitize building name', () => {
      const fileName = PdfService.generateZipFileName('Test & Building #1', '1', '2024');
      expect(fileName).toBe('billing-history-Test---Building--1-1-2024.zip');
    });

    it('should handle missing month and year', () => {
      const fileName = PdfService.generateZipFileName('Test Building');
      const currentYear = new Date().getFullYear();
      expect(fileName).toBe(`billing-history-Test-Building-${currentYear}.zip`);
    });
  });
});
