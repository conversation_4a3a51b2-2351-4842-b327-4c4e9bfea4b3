import { paymentMethodsAPI } from "@/lib/api";
import { PaymentMethod } from "@/types";

export const paymentMethodsService = {
  // Get all payment methods (admin only)
  async getAll(): Promise<{
    data: PaymentMethod[] | null;
    error: string | null;
  }> {
    try {
      const data = await paymentMethodsAPI.getAll();
      return { data, error: null };
    } catch (error: any) {
      console.error("Error fetching payment methods:", error);
      return {
        data: null,
        error:
          error.response?.data?.message || "Failed to fetch payment methods",
      };
    }
  },

  // Get active payment methods for selection
  async getActive(): Promise<{
    data: { id: string; name: string; description?: string }[] | null;
    error: string | null;
  }> {
    try {
      const data = await paymentMethodsAPI.getActive();
      return { data, error: null };
    } catch (error: any) {
      console.error("Error fetching active payment methods:", error);
      return {
        data: null,
        error:
          error.response?.data?.message ||
          "Failed to fetch active payment methods",
      };
    }
  },

  // Get payment method by ID
  async getById(id: string): Promise<{
    data: PaymentMethod | null;
    error: string | null;
  }> {
    try {
      const data = await paymentMethodsAPI.getById(id);
      return { data, error: null };
    } catch (error: any) {
      console.error("Error fetching payment method:", error);
      return {
        data: null,
        error:
          error.response?.data?.message || "Failed to fetch payment method",
      };
    }
  },

  // Create payment method
  async create(paymentMethodData: {
    name: string;
    description?: string;
    isActive?: boolean;
  }): Promise<{
    data: PaymentMethod | null;
    error: string | null;
  }> {
    try {
      const data = await paymentMethodsAPI.create(paymentMethodData);
      return { data, error: null };
    } catch (error: any) {
      console.error("Error creating payment method:", error);
      return {
        data: null,
        error:
          error.response?.data?.message || "Failed to create payment method",
      };
    }
  },

  // Update payment method
  async update(
    id: string,
    updates: {
      name?: string;
      description?: string;
      isActive?: boolean;
    }
  ): Promise<{
    data: PaymentMethod | null;
    error: string | null;
  }> {
    try {
      const data = await paymentMethodsAPI.update(id, updates);
      return { data, error: null };
    } catch (error: any) {
      console.error("Error updating payment method:", error);
      return {
        data: null,
        error:
          error.response?.data?.message || "Failed to update payment method",
      };
    }
  },

  // Delete payment method
  async delete(id: string): Promise<{
    error: string | null;
  }> {
    try {
      await paymentMethodsAPI.delete(id);
      return { error: null };
    } catch (error: any) {
      console.error("Error deleting payment method:", error);
      return {
        error:
          error.response?.data?.message || "Failed to delete payment method",
      };
    }
  },
};
