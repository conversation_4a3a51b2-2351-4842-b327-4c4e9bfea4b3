"use client";

import React, { createContext, useContext, useEffect, useState } from "react";
import { authAPI } from "@/lib/api";
import { User } from "@/types";

interface AuthContextType {
  user: User | null;
  loading: boolean;
  signIn: (identifier: string, password: string) => Promise<{ error?: string }>;
  signUp: (
    email: string,
    username: string,
    password: string
  ) => Promise<{ error?: string }>;
  signOut: () => Promise<void>;
  isAdmin: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Only run on client side
    if (typeof window === "undefined") {
      setLoading(false);
      return;
    }

    // Check for stored user and token
    const storedUser = localStorage.getItem("user");
    const storedToken = localStorage.getItem("auth_token");

    if (storedUser && storedToken) {
      try {
        const parsedUser = JSON.parse(storedUser);
        setUser(parsedUser);

        // Verify token is still valid
        authAPI
          .getProfile()
          .then((response) => {
            setUser(response.user);
            setLoading(false);
          })
          .catch(() => {
            // Token is invalid, clear storage
            localStorage.removeItem("user");
            localStorage.removeItem("auth_token");
            setUser(null);
            setLoading(false);
          });
      } catch (error) {
        // Invalid stored data, clear it
        localStorage.removeItem("user");
        localStorage.removeItem("auth_token");
        setUser(null);
        setLoading(false);
      }
    } else {
      setLoading(false);
    }
  }, []);

  const signIn = async (identifier: string, password: string) => {
    try {
      const response = await authAPI.login(identifier, password);

      // Store token and user data (only on client side)
      if (typeof window !== "undefined") {
        localStorage.setItem("auth_token", response.token);
        localStorage.setItem("user", JSON.stringify(response.user));
      }

      setUser(response.user);
      return {};
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error && "response" in error
          ? (error as { response?: { data?: { message?: string } } }).response
              ?.data?.message
          : "Login failed";
      return { error: errorMessage || "Login failed" };
    }
  };

  const signUp = async (email: string, username: string, password: string) => {
    try {
      const response = await authAPI.register(email, username, password);

      // Store token and user data (only on client side)
      if (typeof window !== "undefined") {
        localStorage.setItem("auth_token", response.token);
        localStorage.setItem("user", JSON.stringify(response.user));
      }

      setUser(response.user);
      return {};
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error && "response" in error
          ? (error as { response?: { data?: { message?: string } } }).response
              ?.data?.message
          : "Registration failed";
      return { error: errorMessage || "Registration failed" };
    }
  };

  const signOut = async () => {
    // Clear stored data (only on client side)
    if (typeof window !== "undefined") {
      localStorage.removeItem("auth_token");
      localStorage.removeItem("user");
    }
    setUser(null);
  };

  const isAdmin = user?.role === "ADMIN" || user?.role === "admin";

  const value = {
    user,
    loading,
    signIn,
    signUp,
    signOut,
    isAdmin,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}
