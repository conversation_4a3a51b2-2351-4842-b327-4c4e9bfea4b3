export interface User {
  id: string;
  email: string;
  username: string;
  role: "admin" | "user" | "ADMIN" | "USER";
  created_at?: string;
  updated_at?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface Building {
  id: string;
  name: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  user?: {
    id: string;
    email: string;
    role: string;
  };
  rooms?: Room[];
  _count?: {
    rooms: number;
  };
}

export interface RoomNumberAndStatus {
  id: string;
  roomNumber: string;
  status: "AVAILABLE" | "OCCUPIED" | "MAINTENANCE";
}

export interface IRoomCard {
  id: string;
  buildingId: string;
  roomNumber: string;
  monthlyRent: number;
  initialWaterReading: number;
  initialElectricityReading: number;
  currentWaterReading: number;
  currentElectricityReading: number;
  status: "AVAILABLE" | "OCCUPIED" | "MAINTENANCE";
  paymentStatus: string;
  currentBillingDate: string | null;
}

export interface Room {
  id: string;
  buildingId: string;
  roomNumber: string;
  monthlyRent: number;
  initialWaterReading: number;
  initialElectricityReading: number;
  currentWaterReading: number;
  currentElectricityReading: number;
  additionalServices?: AdditionalService[];
  status: "AVAILABLE" | "OCCUPIED" | "MAINTENANCE";
  createdAt: string;
  updatedAt: string;
  building?: {
    id: string;
    name: string;
  };
  billingEntries?: BillingEntry[];
  _count?: {
    billingEntries: number;
  };
}

export interface RoomPdf {
  buildingName: string;
  customerName: string;
  roomNumber: string;
  billingEnabledDate: string;
  monthlyRent: number;
  initialWaterReading: number;
  initialElectricityReading: number;
  currentWaterReading: number;
  currentElectricityReading: number;
  waterPricePerUnit: number;
  electricityPricePerUnit: number;
  waterConsumption: number;
  electricityConsumption: number;
  additionalServices?: AdditionalService[];
}

export interface BillingPdf {
  buildingName: string;
  customerName: string;
  roomNumber: string;
  billingEnabledDate: string;
  tableRows: PdfTableRow[];
}

interface PdfTableRow {
  description: string;
  unit: string;
  amount: number;
}

export interface AdditionalService {
  id: string;
  name: string;
  price: number;
  description?: string;
}

export interface BillingEntry {
  id: string;
  roomId: string;
  billingDate: string;
  monthlyRent: number;
  waterReading: number;
  electricityReading: number;
  waterConsumption: number;
  electricityConsumption: number;
  waterCost: number;
  electricityCost: number;
  additionalServicesCost: number;
  totalAmount: number;
  createdBy: string;
  createdAt: string;
  status: "PAID" | "UNPAID" | "PARTIALLY_PAID";
  billingCycleId: string;
}

export interface BillingHistoryEntry {
  id: string;
  room_id: string;
  created_at: string;
  initial_water_reading: number;
  initial_electricity_reading: number;
  electricity_reading: number;
  water_reading: number;
  water_consumption: number;
  electricity_consumption: number;
  total_amount: number;
  status: "PAID" | "UNPAID" | "PARTIALLY_PAID";
  overDueAmount: number;
  monthly_rent: number;
  room_number: string;
}

export interface PaymentEntry {
  id: string;
  billingId: string;
  amount: number;
  paymentDate: string;
  paymentMethodName: string;
  createdAt: string;
  updatedAt: string;
}

export interface PaymentMethod {
  id: string;
  name: string;
  description?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  user?: {
    id: string;
    email: string;
    username: string;
  };
}

export interface BuildingPricing {
  id: string | null;
  buildingId: string;
  waterPricePerUnit: number | string; // Prisma returns Decimal as string
  electricityPricePerUnit: number | string; // Prisma returns Decimal as string
  billingEnabled: boolean;
  updatedBy: string | null;
  updatedAt: string | null;
  user?: {
    id: string;
    email: string;
  } | null;
  building?: {
    id: string;
    name: string;
  };
}

export interface AuthState {
  user: User | null;
  loading: boolean;
  error: string | null;
}

export interface DashboardStats {
  totalBuildings: number;
  totalRooms: number;
  occupiedRooms: number;
  totalMonthlyRevenue: number;
}

export interface Customer {
  id: string;
  nameEn: string;
  nameTh?: string;
  idNumber: string;
  phoneNumber: string;
  address?: string;
  buildingId: string;
  createdAt: string;
  updatedAt: string;
  building?: Building;
  roomAssignments?: CustomerRoomAssignment[];
}

export interface RoomCustomer {
  id: string;
  customerId: string;
  roomId: string;
  assignedAt: string;
  createdAt: string;
  customer?: Customer;
}

export interface CustomerRoomAssignment {
  id: string;
  customerId: string;
  roomId: string;
  assignedAt: string;
  createdAt: string;
  room?: Room;
}

export interface CreateCustomerData {
  nameEn: string;
  nameTh?: string;
  idNumber: string;
  phoneNumber: string;
  address?: string;
  buildingId: string;
}

export interface UpdateCustomerData {
  nameEn?: string;
  nameTh?: string;
  idNumber?: string;
  phoneNumber?: string;
  address?: string;
  buildingId?: string;
}

export interface BillingCycle {
  id: string;
  buildingId: string;
  startDate: string;
  endDate: string;
  updatedAt: string;
  createdBy: string;
  updatedBy: string;
  status: string;
}
