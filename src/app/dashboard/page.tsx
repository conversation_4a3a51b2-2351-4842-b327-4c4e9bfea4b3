"use client";

import React from "react";
import { useRouter } from "next/navigation";
import ProtectedRoute from "@/components/auth/ProtectedRoute";
import { useAuth } from "@/lib/auth";
import Button from "@/components/ui/Button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/Card";
import UserDashboard from "@/components/dashboard/UserDashboard";

const DashboardPage: React.FC = () => {
  const { user, signOut, isAdmin } = useAuth();
  const router = useRouter();

  const handleSignOut = async () => {
    await signOut();
  };

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <header className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-16">
              <div>
                <h1 className="text-xl font-semibold text-gray-900">
                  Apartment Management System
                </h1>
              </div>
              <div className="flex items-center space-x-4">
                <span className="text-sm text-gray-600">
                  Welcome, {user?.username || user?.email}
                </span>
                {isAdmin && (
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    Admin
                  </span>
                )}
                <Button onClick={handleSignOut} variant="outline" size="sm">
                  Sign Out
                </Button>
              </div>
            </div>
          </div>
        </header>

        {/* Main Content */}
        <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="px-4 py-6 sm:px-0">
            {isAdmin ? (
              // Admin Dashboard
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {/* Welcome Card */}
                <Card className="col-span-full">
                  <CardHeader>
                    <CardTitle>Admin Dashboard</CardTitle>
                    <CardDescription>
                      As an admin, you have full access to manage buildings,
                      rooms, billing, and users.
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-wrap gap-4">
                      <Button onClick={() => router.push("/buildings")}>
                        Manage Buildings
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => router.push("/admin/customers")}
                      >
                        Manage Customers
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => router.push("/admin")}
                      >
                        Admin Settings
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => router.push("/billing")}
                      >
                        Billing Dashboard
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
            ) : (
              // User Dashboard
              <UserDashboard />
            )}
          </div>
        </main>
      </div>
    </ProtectedRoute>
  );
};

export default DashboardPage;
