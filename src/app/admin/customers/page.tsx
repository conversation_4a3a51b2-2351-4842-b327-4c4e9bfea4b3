"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import ProtectedRoute from "@/components/auth/ProtectedRoute";
import { useAuth } from "@/lib/auth";
import { buildingService } from "@/lib/services/buildings";
import { customersService } from "@/lib/services/customers";
import { Building, Customer, CreateCustomerData } from "@/types";
import Button from "@/components/ui/Button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/Card";
import CustomerCard from "@/components/customers/CustomerCard";
import CustomerForm from "@/components/customers/CustomerForm";
import CustomerDetailsModal from "@/components/customers/CustomerDetailsModal";
import {
  ArrowLeft,
  Users,
  Building as BuildingIcon,
  Search,
} from "lucide-react";

const CustomerManagementPage: React.FC = () => {
  const { user, signOut, isAdmin } = useAuth();
  const router = useRouter();

  // State
  const [buildings, setBuildings] = useState<Building[]>([]);
  const [selectedBuildingId, setSelectedBuildingId] = useState<string>("");
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(
    null
  );
  const [showCustomerModal, setShowCustomerModal] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");

  // Loading states
  const [loadingBuildings, setLoadingBuildings] = useState(true);
  const [loadingCustomers, setLoadingCustomers] = useState(false);
  const [creatingCustomer, setCreatingCustomer] = useState(false);

  // Messages
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Filter customers based on search term (nameEn, phoneNumber, idNumber)
  const filteredCustomers = customers.filter((customer) => {
    if (!searchTerm) return true;

    const searchLower = searchTerm.toLowerCase();
    return (
      customer.nameEn.toLowerCase().includes(searchLower) ||
      customer.phoneNumber.toLowerCase().includes(searchLower) ||
      customer.idNumber.toLowerCase().includes(searchLower)
    );
  });

  useEffect(() => {
    fetchBuildings();
  }, []);

  useEffect(() => {
    if (selectedBuildingId) {
      fetchCustomers();
    } else {
      setCustomers([]);
    }
    // Clear search term when building changes
    setSearchTerm("");
  }, [selectedBuildingId]);

  const fetchBuildings = async () => {
    setLoadingBuildings(true);
    setError(null);

    try {
      const { data, error } = await buildingService.getBuildings();
      if (error) {
        setError(error);
      } else if (data) {
        setBuildings(data);
        // Auto-select first building if only one
        if (data.length === 1) {
          setSelectedBuildingId(data[0].id);
        }
      }
    } catch (err) {
      setError("Failed to fetch buildings");
    } finally {
      setLoadingBuildings(false);
    }
  };

  const fetchCustomers = async () => {
    if (!selectedBuildingId) return;

    setLoadingCustomers(true);
    setError(null);

    try {
      const { data, error } = await customersService.getByBuilding(
        selectedBuildingId
      );
      if (error) {
        setError(error);
      } else if (data) {
        setCustomers(data);
      }
    } catch (err) {
      setError("Failed to fetch customers");
    } finally {
      setLoadingCustomers(false);
    }
  };

  const handleCreateCustomer = async (customerData: CreateCustomerData) => {
    setCreatingCustomer(true);
    setError(null);
    setSuccess(null);

    try {
      const { error } = await customersService.create(customerData);
      if (error) {
        setError(error);
      } else {
        setSuccess("Customer created successfully");
        fetchCustomers(); // Refresh the list
      }
    } catch (err) {
      setError("Failed to create customer");
    } finally {
      setCreatingCustomer(false);
    }
  };

  const handleCustomerClick = (customer: Customer) => {
    setSelectedCustomer(customer);
    setShowCustomerModal(true);
  };

  const handleCustomerUpdate = () => {
    fetchCustomers(); // Refresh the list
    setSuccess("Customer updated successfully");
  };

  const handleSignOut = async () => {
    await signOut();
  };

  const clearMessages = () => {
    setError(null);
    setSuccess(null);
  };

  return (
    <ProtectedRoute requireAdmin={true}>
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <header className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-16">
              <div className="flex items-center space-x-4">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => router.push("/dashboard")}
                  className="flex items-center space-x-2"
                >
                  <ArrowLeft size={16} />
                  <span>Dashboard</span>
                </Button>
                <div>
                  <h1 className="text-xl font-semibold text-gray-900">
                    Customer Management
                  </h1>
                  <p className="text-sm text-gray-500">
                    Manage customers and room assignments
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <span className="text-sm text-gray-600">
                  Welcome, {user?.email}
                </span>
                {isAdmin && (
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    Admin
                  </span>
                )}
                <Button onClick={handleSignOut} variant="outline" size="sm">
                  Sign Out
                </Button>
              </div>
            </div>
          </div>
        </header>

        {/* Main Content */}
        <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="px-4 py-6 sm:px-0 space-y-6">
            {/* Global Messages */}
            {success && (
              <div className="p-3 bg-green-50 border border-green-200 rounded-md">
                <p className="text-sm text-green-600">{success}</p>
                <button
                  onClick={clearMessages}
                  className="text-green-600 hover:text-green-700 text-xs underline ml-2"
                >
                  Dismiss
                </button>
              </div>
            )}

            {error && (
              <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                <p className="text-sm text-red-600">{error}</p>
                <button
                  onClick={clearMessages}
                  className="text-red-600 hover:text-red-700 text-xs underline ml-2"
                >
                  Dismiss
                </button>
              </div>
            )}

            {/* Building Selection */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <BuildingIcon className="h-5 w-5" />
                  <span>Select Building</span>
                </CardTitle>
                <CardDescription>
                  Choose a building to view and manage its customers
                </CardDescription>
              </CardHeader>
              <CardContent>
                {loadingBuildings ? (
                  <div className="flex items-center justify-center py-4">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                  </div>
                ) : (
                  <select
                    value={selectedBuildingId}
                    onChange={(e) => setSelectedBuildingId(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">Select a building...</option>
                    {buildings.map((building) => (
                      <option key={building.id} value={building.id}>
                        {building.name}
                      </option>
                    ))}
                  </select>
                )}
              </CardContent>
            </Card>

            {/* Customer Management Content */}
            {selectedBuildingId ? (
              <div className="space-y-6">
                {/* Customer List */}
                <Card>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="flex items-center space-x-2">
                        <Users className="h-5 w-5" />
                        <span>Customers</span>
                      </CardTitle>
                      <span className="text-sm text-gray-600">
                        {searchTerm
                          ? `${filteredCustomers.length} of ${customers.length} customers`
                          : `${customers.length} customer${
                              customers.length !== 1 ? "s" : ""
                            } total`}
                      </span>
                    </div>

                    {/* Search Input */}
                    {customers.length > 0 && (
                      <div className="mt-4 relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <Search className="h-4 w-4 text-gray-400" />
                        </div>
                        <input
                          type="text"
                          placeholder="Search by name, phone number, or ID number..."
                          value={searchTerm}
                          onChange={(e) => setSearchTerm(e.target.value)}
                          className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-700"
                        />
                      </div>
                    )}
                  </CardHeader>
                  <CardContent>
                    {loadingCustomers ? (
                      <div className="flex items-center justify-center py-8">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                      </div>
                    ) : customers.length === 0 ? (
                      <div className="text-center py-8">
                        <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                        <p className="text-gray-500">No customers found</p>
                        <p className="text-sm text-gray-400">
                          Add your first customer using the form below
                        </p>
                      </div>
                    ) : filteredCustomers.length === 0 ? (
                      <div className="text-center py-8">
                        <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                        <p className="text-gray-500">
                          No customers found matching "{searchTerm}"
                        </p>
                        <p className="text-sm text-gray-400">
                          Try searching by name, phone number, or ID number
                        </p>
                      </div>
                    ) : (
                      <div
                        className="flex flex-col gap-3 max-h-96 overflow-y-auto"
                        style={{ maxHeight: "400px" }} // Exactly 10 cards height
                      >
                        {filteredCustomers.map((customer) => (
                          <CustomerCard
                            key={customer.id}
                            customer={customer}
                            onClick={handleCustomerClick}
                          />
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Add Customer Form */}
                <CustomerForm
                  buildingId={selectedBuildingId}
                  onSubmit={handleCreateCustomer}
                  loading={creatingCustomer}
                  error={null} // We handle errors globally
                  success={null} // We handle success globally
                />
              </div>
            ) : (
              <Card>
                <CardContent className="text-center py-12">
                  <BuildingIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    Select a building to view customers
                  </h3>
                  <p className="text-gray-500">
                    Choose a building from the dropdown above to start managing
                    customers
                  </p>
                </CardContent>
              </Card>
            )}
          </div>
        </main>

        {/* Customer Details Modal */}
        <CustomerDetailsModal
          customer={selectedCustomer}
          isOpen={showCustomerModal}
          onClose={() => {
            setShowCustomerModal(false);
            setSelectedCustomer(null);
          }}
          onUpdate={handleCustomerUpdate}
        />
      </div>
    </ProtectedRoute>
  );
};

export default CustomerManagementPage;
