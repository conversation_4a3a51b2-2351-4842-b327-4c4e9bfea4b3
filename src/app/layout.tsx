import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON>ei<PERSON>, <PERSON>eist_Mono } from "next/font/google";
import "./globals.css";
import { AuthProvider } from "@/lib/auth";
import ErrorBoundary from "@/components/ui/ErrorBoundary";
import DemoModeBanner from "@/components/ui/DemoModeBanner";
import ApiConfigProvider from "@/components/ApiConfigProvider";
import ApiConfigDebug from "@/components/ApiConfigDebug";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Apartment Management System",
  description: "Comprehensive apartment management application",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <ApiConfigProvider>
          <ErrorBoundary>
            <DemoModeBanner />
            <AuthProvider>{children}</AuthProvider>
            <ApiConfigDebug />
          </ErrorBoundary>
        </ApiConfigProvider>
      </body>
    </html>
  );
}
