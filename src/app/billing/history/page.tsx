"use client";

import React from "react";
import { useRouter } from "next/navigation";
import ProtectedRoute from "@/components/auth/ProtectedRoute";
import BillingDashboard from "@/components/billing/BillingDashboard";
import { useAuth } from "@/lib/auth";
import Button from "@/components/ui/Button";
import { ArrowLeft } from "lucide-react";
import BillingHistory from "@/components/billing/BillingHistory";

const BillingHistoryPage: React.FC = () => {
  const { user, signOut, isAdmin } = useAuth();
  const router = useRouter();

  const handleSignOut = async () => {
    await signOut();
  };

  return (
    <BillingHistory
      roomId="dcb1c078-d130-4a54-a04d-802d3eb3d297"
      roomNumber="101"
    />
  );
};

export default BillingHistoryPage;
